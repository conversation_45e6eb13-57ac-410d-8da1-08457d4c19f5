// ==UserScript==
// @name         smth smth
// @match        *://*.moomoo.io/*
// @match        *://*.dev.moomoo.io/*
// @grant        none
// @version      v1
// <AUTHOR>
// @description  mod pls
// @require      https://rawgit.com/kawanet/msgpack-lite/master/dist/msgpack.min.js
// ==/UserScript==

        let whereverifybutton = document.querySelector("#altcha_checkbox")
        function clicktheverify() {
            if (whereverifybutton) {
                whereverifybutton.click()
            }
        }
        setInterval(clicktheverify, 1000);

//This is variable for websocket
let ws = [];
(function(modules) {
    let cache = {};
    function require(id) {
        if (cache[id]) return cache[id].exports;
        let module = {
            exports: {}
        };
        modules[id](module, module.exports, require);
        cache[id] = module;
        return module.exports;
    }
    require.s = "./main/apps.js";
    return require(require.s);
})({
    "./main/apps.js": function(module, exports, require) {
        "use strict";
        // IS JOSH:
        var isProd = window.location.hostname == "127.0.0.1" && location.hostname.startWith("192.168.");

        // CONFIG:
        var config = require("./modules/config.js")
        if (config == undefined || null) config = window.config;

        // ITEMS:
        var items = require("./modules/items.js");

        // Websocket stuff erm;
        let packeteres = {
            "pp": "0",
            "sp": "M",
            "2": "D",
            "7": "K",
            "14": "S",
            "c": "F",
            "5": "z",
            "13c": "c",
            "33": "9",
            "6": "H",
            "8": "L",
            "9": "N",
            "10": "b",
            "11": "P",
            "12": "Q",
            "rmd": "e",
            "ch": "6",
        }

        var soc = {
            connected: false,
            socket: null,
            socketId: null,
            connect: function(callback, events) {
                if (this.socket) return;
                let _this = this;
                let socketError = false;
                WebSocket.prototype.sends = WebSocket.prototype.send;
                WebSocket.prototype.send = function(message) {
                    if (!_this.socket) {
                        _this.socket = this;
                        ws = _this.socket;
                        _this.socket.addEventListener("message", (event) => {
                            let data = new Uint8Array(event.data);
                            let parsed = window.msgpack.decode(data);
                            let type = parsed[0];
                            data = parsed[1];
                            if (type == "io-init") {
                                _this.socketId = data[0];
                            } else {
                                if (events[type]) {
                                    events[type].apply(undefined, data);
                                }
                            }
                        });
                        _this.connected = true;
                        _this.socket.addEventListener("open", () => {
                            _this.connected = true;
                            callback();
                        });
                        _this.socket.addEventListener("close", (event) => {
                            _this.connected = false;
                            if (event.code == 4001) {
                                callback("Invalid Connection");
                            } else if (!socketError) {
                                callback("disconnected");
                            }
                        });
                        _this.socket.addEventListener("error", (error) => {
                            if (this.socket && this.socket.readyState != WebSocket.OPEN) {
                                socketError = true;
                                callback("Socket error");
                            }
                        });
                    }
                    this.sends(message);
                }
            },

            socketReady: function() {
                return this.socket && this.connected;
            },

            close: function(reason) {
                if (this.socket) {
                    this.socket.close();
                    this.socket = null;
                    this.connected = false;
                    console.error("Socket closed:", reason || "No reason provided");
                }
            },

            send: function(type) {
                if (this.socketReady()) {
                    let data = Array.prototype.slice.call(arguments, 1);
                    let message = window.msgpack.encode([type, data]);
                    this.socket.send(message);
                }
            },

            fixSend: function(type) {
                let fixedType = packeteres[type] || type;
                this.send(fixedType);
            }
        };


        function disconnect(error) {
            soc.close(error);
        }

        // UIS:
        var getEl = (id) => document.getElementById(id);
        var UIS = require("./modules/uis.js");
        var menuText = UIS.global.menuText;
        var setupCard = UIS.global.setupCard;
        var guideCard = UIS.global.guideCard;
        var gameName = UIS.global.gameName;
        var gameUI = UIS.global.gameUI;
        var mainMenu = UIS.global.mainMenu;
        var storeMenu = UIS.global.storeMenu;
        var nameInput = UIS.global.nameInput;
        var gameCanvas = UIS.global.gameCanvas;
        var gameContext = UIS.global.gameContext;
        var mapDisplay = UIS.global.mapDisplay;
        var mapContext = UIS.global.mapContext;
        var shutdownDisplay = UIS.global.shutdownDisplay;
        var pingDisplay = UIS.global.pingDisplay;
        var loadingText = UIS.global.loadingText;
        var diedText = UIS.global.diedText;
        var ageText = UIS.global.ageText;
        var ageBarBody = UIS.global.ageBarBody;
        var allianceMenu = UIS.global.allianceMenu;
        var allianceManager = UIS.global.allianceManager;
        var notificationDisplay = UIS.global.notificationDisplay;
        var leaderboardData = UIS.global.leaderboardData;
        var actionBar = UIS.global.actionBar;
        var playMusic = UIS.global.playMusic;
        var upgradeCounter = UIS.global.upgradeCounter;
        var chatBox = UIS.global.chatBox;

        // BUTTON:
        var storeButton = UIS.buttons.store;
        var allianceButton = UIS.buttons.alliance;
        var chatButton = UIS.buttons.chat;
        var enterGameButton = UIS.buttons.enterGame;
        var partyButton = UIS.buttons.partyButton;
        var joinB = UIS.buttons.joinB;
        var settingsButton = UIS.buttons.settingsButton;
        var settingsButtonTitle = UIS.buttons.settingsButtonTitle;

        // RESOURCE:
        var foodDisplay = UIS.resources.food;
        var woodDisplay = UIS.resources.wood;
        var stoneDisplay = UIS.resources.stone;
        var scoreDisplay = UIS.resources.score;
        var killCounter = UIS.resources.kill;

        // ADS:
        var adCard = UIS.ads.adCard;
        var adContainer = UIS.ads.adContainer;
        var promoImg = UIS.ads.promoImg;
        var promoImageHolder = UIS.ads.promoImageHolder;
        var wideAdCard = UIS.ads.wideAdCard;

        // HOLDER:
        var menuCardHolder = UIS.holder.menuCardHolder;
        var itemInfoHolder = UIS.holder.itemInfoHolder;
        var upgradeHolder = UIS.holder.upgradeHolder;
        var allianceHolder = UIS.holder.allianceHolder;
        var skinColorHolder = UIS.holder.skinColorHolder;
        var storeHolder = UIS.holder.storeHolder;
        var chatHolder = UIS.holder.chatHolder;

        // ATCHA:
        var alcha = UIS.global.altcha
        var alchaCheckBox = UIS.buttons.altchaCheck;

        // SETTING:
        var serverBrowser = UIS.server.serverBrowser;
        var nativeResolutionOption = UIS.server.nativeResolutionOption;
        var showPingOption = UIS.server.showPingOption;

        // CTX || CONTEXT:
        var canvas = document.createElement("canvas");
        var ctx = gameCanvas.getContext("2d");
        canvas.id = "canvas";
        canvas.style = `
        z-index: 1;`;
        document.body.appendChild(canvas);

        // GLOBAL VALUES:
        var firstSetup = true
        var moofoll = false;
        if (typeof (Storage) !== "undefined") {
            moofoll = true;
        }

        // SCREEN:
        var sW = window.innerWidth;
        var sH = window.innerHeight;
        var screen = {
            max: {
                height: config.maxScreenHeight,
                width: config.maxScreenWidth,
            },
            current: {
                width: sW * window.devicePixelRatio,
                height: sH * window.devicePixelRatio,
            }
        };
        var xOffset;
        var yOffset;

        var camX;
        var camY;

        // MOUSE TRACKERS:
        var attackState = 0;

        var mouseX = 0;
        var mouseY = 0;

        // MOUSE INPUT:
        var mals = document.getElementById('touch-controls-fullscreen');
        mals.style.display = 'block';

        mals.addEventListener("mousemove", (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        }, { passive: true });

        let clicks = {
            left: false,
            middle: false,
            right: false,
        };

        mals.addEventListener("pointerdown", (e) => {
            if (e.button === 0) {
                clicks.left = true;
            } else if (e.button === 1) {
                clicks.middle = true;
            } else if (e.button === 2) {
                clicks.right = true;
            }
            attackState = 1;
        }, false);

        mals.addEventListener("pointerup", (e) => {
            if (e.button === 0) {
                clicks.left = false;
            } else if (e.button === 1) {
                clicks.middle = false;
            } else if (e.button === 2) {
                clicks.right = false;
            }
            attackState = 0;
        }, false);

        var wbe = 1;
        var mWbe = 1;
        var smothAnim = null;
        mals.addEventListener("wheel", (e) => {
            if (e.deltaY < 0) {

                if (screen.max.width > 800) {
                    mWbe = Math.max(mWbe - 0.35, 0.1);
                }
            } else {
                mWbe = Math.min(mWbe + 0.35, 10);
            }
            if (smothAnim) clearInterval(smothAnim);
            smothAnim = setInterval(() => {
                wbe += (mWbe - wbe) * 0.1;
                screen.max.width = config.maxScreenWidth * wbe;
                screen.max.height = config.maxScreenHeight * wbe;
                resize();
                if (Math.abs(mWbe - wbe) < 0.01) {
                    clearInterval(smothAnim);
                }
            }, 15);
        }, { passive: true });



        var ais = [];
        var players = [];
        var alliances = [];
        var alliancePlayers = [];
        var allianceNotifications = [];
        var gameObjects = [];
        var projectiles = [];

        var breakObjects = [];
        var tmpDir;
        var player;
        var playerSID;
        var tmpObj;

        var enemy = [];
        var nears = [];
        var near = [];



        var pixelDensity = 1;
        var delta;
        var now;
        var lastUpdate = performance.now();


        var waterMult = 1;
        var waterPlus = 0;

        var outlineColor = "#525252";
        var darkOutlineColor = "#3d3f42";
        var outlineWidth = 5.5;

        var keys = {};
        var moveKeys = {
            87: [0, -1],
            38: [0, -1],
            83: [0, 1],
            40: [0, 1],
            65: [-1, 0],
            37: [-1, 0],
            68: [1, 0],
            39: [1, 0],
        };
        var inGame = false;

        var macro = {};

        var mills = {
            place: 0,
            placeSpawnPads: 0
        };

        var lastDir;

        var lastLeaderboardData = [];

        // ON LOAD:
        var inWindow = true;
        window.onblur = function () {
            inWindow = false;
        };
        window.onfocus = function () {
            inWindow = true;
        };

        var placeVisible = [];
        var profanityList = ["cunt", "whore", "fuck", "shit", "faggot", "nigger",
                             "nigga", "dick", "vagina", "minge", "cock", "rape", "cum", "sex",
                             "tits", "penis", "clit", "pussy", "meatcurtain", "jizz", "prune",
                             "douche", "wanker", "damn", "bitch", "dick", "fag", "bastard"];

        // VOLCANO
        var volcano = {
            animationTime: 0,
            land: null,
            lava: null,
            x: config.volcanoLocationX,
            y: config.volcanoLocationY,
        };








        // FIND OBJECTS BY ID/SID:
        function findID(tmpObj, tmp) {
            return tmpObj.find((THIS) => THIS.id == tmp);
        }

        function findSID(tmpObj, tmp) {
            return tmpObj.find((THIS) => THIS.sid == tmp);
        }

        function findPlayerByID(id) {
            return findID(players, id);
        }

        function findPlayerBySID(sid) {
            return findSID(players, sid);
        }

        function findAIBySID(sid) {
            return findSID(ais, sid);
        }

        function findObjectBySid(sid) {
            return findSID(gameObjects, sid);
        }

        function findProjectileBySid(sid) {
            return findSID(gameObjects, sid);
        }
        // MATHS:
        let math = [
            Math.PI, // 0
            Math.abs, // 1
            Math.sqrt, // 2
            Math.pow, // 3
            Math.round, // 4
            Math.floor, // 5
            Math.ceil, // 6
            Math.max, // 7
            Math.min, // 8
            Math.random, // 9
            Math.sin, // 10
            Math.cos, // 11
            Math.tan, // 12
            Math.log, // 13
            Math.exp, // 14
            Math.sign, // 15
            Math.atan2, // 16
        ];
        let pi_ = {
            _1: Math.PI,
            _15: Math.PI * 1.5,
            _2: Math.PI * 2,
            D2: Math.PI / 2,
            D3: Math.PI / 3,
            D4: Math.PI / 4,
            D8: Math.PI / 8,
            D16: Math.PI / 16,
            D32: Math.PI / 32,
        }

        // utils:
        var utils = require("./modules/utils.js");


        let WS = undefined;
        let socketID = undefined;

        let secPacket = 0;
        let secMax = 110;
        let secTime = 1000;
        let firstSend = {
            sec: false
        };
        let game = {
            tick: 0,
            tickQueue: [],
            tickBase: function (set, tick) {
                if (this.tickQueue[this.tick + tick]) {
                    this.tickQueue[this.tick + tick].push(set);
                } else {
                    this.tickQueue[this.tick + tick] = [set];
                }
            },
            tickRate: (1000 / config.serverUpdateRate),
            tickSpeed: 0,
            lastTick: performance.now()
        };
        let modConsole = [];

        let dontSend = false;
        let fpsTimer = {
            last: 0,
            time: 0,
            ltime: 0
        }
        let lastMoveDir = undefined;
        let lastsp = ["cc", 1, "__proto__"];

        // InGame Text:
        let texts = [];
        class textCore {
            constructor(x, y, value, type, scale, color, index) {
                this.x = x;
                this.y = y;
                this.value = value;
                this.type = type;
                this.scale = scale;
                this.color = color;
                this.index = index;
                this.alpha = 0;
                this.maxTime = 1300;
                this.time = this.maxTime;
                this.shaking = utils.randFloat(-1, 1);
                this.accelerate = 1;
            }
        }
        let textC = new textCore;
        function renderTexts(delta) {
            texts = texts.filter(data => data.time > 0);
            ctx.lineWidth = 8;
            ctx.strokeStyle = darkOutlineColor;
            ctx.textBaseline = "middle";
            ctx.textAlign = "center";
            for (let i = 0; i < texts.length; i++) {
                let data = texts[i];

                data.x += data.shaking;
                data.y -= (delta / 10) * data.accelerate;

                if (data.time < data.maxTime / 2) {
                    if (data.alpha > 0) {
                        data.alpha = math[7](0, data.alpha - (delta / (data.maxTime / 2.5)));
                    }
                } else {
                    if (data.alpha < 1) {
                        data.alpha = math[8](1, data.alpha + (delta / 100));
                    }
                }

                let fx = data.x - xOffset;
                let fy = data.y - yOffset;

                ctx.globalAlpha = data.alpha;
                ctx.font = data.scale + "px Hammersmith One";
                ctx.fillStyle = data.color;
                ctx.strokeText(data.value, fx, fy);
                ctx.fillText(data.value, fx, fy);

                data.accelerate -= delta / (data.maxTime / 2.5);
                data.time -= delta;
            }
            ctx.textBaseline = "alphabetic";
        }

        class GameObject {
            constructor(sid) {
                this.sid = sid;

                // INIT:
                this.init = function (x, y, dir, scale, type, data, owner) {
                    data = data || {};
                    this.sentTo = {};
                    this.gridLocations = [];
                    this.active = true;
                    this.alive = true;
                    this.doUpdate = data.doUpdate;
                    this.x = x;
                    this.y = y;

                    this.dir = dir;

                    this.lastDir = dir;
                    this.xWiggle = 0;
                    this.yWiggle = 0;
                    this.visScale = scale;
                    this.scale = scale;
                    this.type = type;
                    this.id = data.id;
                    this.owner = owner;
                    this.name = data.name;
                    this.isItem = (this.id != undefined);
                    this.group = data.group;
                    this.maxHealth = data.health;
                    this.health = this.maxHealth;
                    this.layer = 2;
                    if (this.group != undefined) {
                        this.layer = this.group.layer;
                    } else if (this.type == 0) {
                        this.layer = 3;
                    } else if (this.type == 2) {
                        this.layer = 0;
                    } else if (this.type == 4) {
                        this.layer = -1;
                    }
                    this.colDiv = data.colDiv || 1;
                    this.blocker = data.blocker;
                    this.ignoreCollision = data.ignoreCollision;
                    this.dontGather = data.dontGather;
                    this.hideFromEnemy = data.hideFromEnemy;
                    this.friction = data.friction;
                    this.projDmg = data.projDmg;
                    this.dmg = data.dmg;
                    this.pDmg = data.pDmg;
                    this.pps = data.pps;
                    this.zIndex = data.zIndex || 0;
                    this.turnSpeed = data.turnSpeed;
                    this.req = data.req;
                    this.trap = data.trap;
                    this.healCol = data.healCol;
                    this.teleport = data.teleport;
                    this.boostSpeed = data.boostSpeed;
                    this.projectile = data.projectile;
                    this.shootRange = data.shootRange;
                    this.shootRate = data.shootRate;
                    this.shootCount = this.shootRate;
                    this.spawnPoint = data.spawnPoint;
                    this.onNear = 0;
                    this.breakObj = false;
                    this.alpha = data.alpha||1;
                    this.maxAlpha = data.alpha||1;
                    this.damaged = 0;
                    this.tSpd = 0;
                    this.turnPower = 0;
                };

                // GET HIT:
                this.changeHealth = function (amount, doer) {
                    this.health += amount;
                    return (this.health <= 0);
                };

                // GET SCALE:
                this.getScale = function (sM, ig) {
                    sM = sM || 1;
                    return this.scale * ((this.isItem || this.type == 2 || this.type == 3 || this.type == 4) ?
                                         1 : (0.6 * sM)) * (ig ? 1 : this.colDiv);
                };

                // VISIBLE TO PLAYER:
                this.visibleToPlayer = function (player) {
                    return !(this.hideFromEnemy) || (this.owner && (this.owner == player ||
                                                                    (this.owner.team && player.team == this.owner.team)));
                };

                // UPDATE:
                this.update = function (delta) {
                    if (this.active) {
                        if (this.xWiggle) {
                            this.xWiggle *= Math.pow(0.99, delta);
                        }
                        if (this.yWiggle) {
                            this.yWiggle *= Math.pow(0.99, delta);
                        }

                        if (this.turnSpeed) {
                            this.dir += this.turnSpeed * delta;
                        }
                        if (this.hideFromEnemy && this.isTeamObject(player)) {
                            for (let i of enemy) {
                                let nearDist = utils.getDist({x: this.x, y: this.y}, i, 0, 2)
                                if (nearDist < this.scale + i.scale - 35) {
                                    this.hideFromEnemy = false
                                }
                            }
                        }
                    } else {
                        if (this.alive) {
                            this.alpha -= delta / (200 / this.maxAlpha);
                            this.visScale += delta / (this.scale / 2.5);
                            if (this.alpha <= 0) {
                                this.alpha = 0;
                                this.alive = false;
                            }
                        }
                    }
                };

                // CHECK TEAM:
                this.isTeamObject = function (tmpObj) {
                    return this.owner == null ? true : (this.owner && tmpObj.sid == this.owner.sid || tmpObj.findAllianceBySid(this.owner.sid));
                };
            }
        }

        class Objectmanager {
            constructor(GameObject, gameObjects, utils, config, players, server) {
                this.ignoreAdd = false;
                this.hitObj = [];
                this.disableObj = function (obj) {
                    obj.active = false;

                };
                let tmpObj;
                this.add = function (sid, x, y, dir, s, type, data, setSID, owner) {
                    tmpObj = findObjectBySid(sid);
                    if (!tmpObj) {
                        tmpObj = gameObjects.find((tmp) => !tmp.active);
                        if (!tmpObj) {
                            tmpObj = new GameObject(sid);
                            gameObjects.push(tmpObj);
                        }
                    }
                    if (setSID) {
                        tmpObj.sid = sid;
                    }
                    tmpObj.init(x, y, dir, s, type, data, owner);
                };
                this.disableBySid = function (sid) {
                    let find = findObjectBySid(sid);
                    if (find) {
                        this.disableObj(find);
                    }
                };
                this.removeAllItems = function (sid, server) {
                    gameObjects.filter((tmp) => tmp.active && tmp.owner && tmp.owner.sid == sid).forEach((tmp) => this.disableObj(tmp));
                };
                this.checkItemLocation = function (x, y, s, sM, indx, ignoreWater, placer) {
                    let cantPlace = gameObjects.find((tmp) => tmp.active && utils.getDistance(x, y, tmp.x, tmp.y) < s + (tmp.blocker ? tmp.blocker : tmp.getScale(sM, tmp.isItem)));
                    if (cantPlace) return false;
                    if (!ignoreWater && indx != 18 && y >= config.mapScale / 2 - config.riverWidth / 2 && y <= config.mapScale / 2 + config.riverWidth / 2) return false;
                    return true;
                };

            }
        }
        class Projectile {
            constructor(players, ais, objectManager, items, config, utils, server) {

                // INIT:
                this.init = function (indx, x, y, dir, spd, dmg, rng, scl, owner) {
                    this.active = true;
                    this.tickActive = true;
                    this.indx = indx;
                    this.x = x;
                    this.y = y;
                    this.x2 = x;
                    this.y2 = y;
                    this.dir = dir;
                    this.skipMov = true;
                    this.speed = spd;
                    this.dmg = dmg;
                    this.scale = scl;
                    this.range = rng;
                    this.r2 = rng;
                    this.owner = owner;
                };

                // UPDATE:
                this.update = function (delta) {
                    if (this.active) {
                        let tmpSpeed = this.speed * delta;
                        if (!this.skipMov) {
                            this.x += tmpSpeed * Math.cos(this.dir);
                            this.y += tmpSpeed * Math.sin(this.dir);
                            this.range -= tmpSpeed;
                            if (this.range <= 0) {
                                this.x += this.range * Math.cos(this.dir);
                                this.y += this.range * Math.sin(this.dir);
                                tmpSpeed = 1;
                                this.range = 0;
                                this.active = false;
                            }
                        } else {
                            this.skipMov = false;
                        }
                    }
                };
                this.tickUpdate = function (delta) {
                    if (this.tickActive) {
                        let tmpSpeed = this.speed * delta;
                        if (!this.skipMov) {
                            this.x2 += tmpSpeed * Math.cos(this.dir);
                            this.y2 += tmpSpeed * Math.sin(this.dir);
                            this.r2 -= tmpSpeed;
                            if (this.r2 <= 0) {
                                this.x2 += this.r2 * Math.cos(this.dir);
                                this.y2 += this.r2 * Math.sin(this.dir);
                                tmpSpeed = 1;
                                this.r2 = 0;
                                this.tickActive = false;
                            }
                        } else {
                            this.skipMov = false;
                        }
                    }
                };
            }
        };
        class Store {
            constructor() {
                // STORE HATS:
                this.hats = [{
                    id: 45,
                    name: "Shame!",
                    dontSell: true,
                    price: 0,
                    scale: 120,
                    desc: "hacks are for winners"
                }, {
                    id: 51,
                    name: "Moo Cap",
                    price: 0,
                    scale: 120,
                    desc: "coolest mooer around"
                }, {
                    id: 50,
                    name: "Apple Cap",
                    price: 0,
                    scale: 120,
                    desc: "apple farms remembers"
                }, {
                    id: 28,
                    name: "Moo Head",
                    price: 0,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 29,
                    name: "Pig Head",
                    price: 0,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 30,
                    name: "Fluff Head",
                    price: 0,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 36,
                    name: "Pandou Head",
                    price: 0,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 37,
                    name: "Bear Head",
                    price: 0,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 38,
                    name: "Monkey Head",
                    price: 0,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 44,
                    name: "Polar Head",
                    price: 0,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 35,
                    name: "Fez Hat",
                    price: 0,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 42,
                    name: "Enigma Hat",
                    price: 0,
                    scale: 120,
                    desc: "join the enigma army"
                }, {
                    id: 43,
                    name: "Blitz Hat",
                    price: 0,
                    scale: 120,
                    desc: "hey everybody i'm blitz"
                }, {
                    id: 49,
                    name: "Bob XIII Hat",
                    price: 0,
                    scale: 120,
                    desc: "like and subscribe"
                }, {
                    id: 57,
                    name: "Pumpkin",
                    price: 50,
                    scale: 120,
                    desc: "Spooooky"
                }, {
                    id: 8,
                    name: "Bummle Hat",
                    price: 100,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 2,
                    name: "Straw Hat",
                    price: 500,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 15,
                    name: "Winter Cap",
                    price: 600,
                    scale: 120,
                    desc: "allows you to move at normal speed in snow",
                    coldM: 1
                }, {
                    id: 5,
                    name: "Cowboy Hat",
                    price: 1000,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 4,
                    name: "Ranger Hat",
                    price: 2000,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 18,
                    name: "Explorer Hat",
                    price: 2000,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 31,
                    name: "Flipper Hat",
                    price: 2500,
                    scale: 120,
                    desc: "have more control while in water",
                    watrImm: true
                }, {
                    id: 1,
                    name: "Marksman Cap",
                    price: 3000,
                    scale: 120,
                    desc: "increases arrow speed and range",
                    aMlt: 1.3
                }, {
                    id: 10,
                    name: "Bush Gear",
                    price: 3000,
                    scale: 160,
                    desc: "allows you to disguise yourself as a bush"
                }, {
                    id: 48,
                    name: "Halo",
                    price: 3000,
                    scale: 120,
                    desc: "no effect"
                }, {
                    id: 6,
                    name: "Soldier Helmet",
                    price: 4000,
                    scale: 120,
                    desc: "reduces damage taken but slows movement",
                    spdMult: 0.94,
                    dmgMult: 0.75
                }, {
                    id: 23,
                    name: "Anti Venom Gear",
                    price: 4000,
                    scale: 120,
                    desc: "makes you immune to poison",
                    poisonRes: 1
                }, {
                    id: 13,
                    name: "Medic Gear",
                    price: 5000,
                    scale: 110,
                    desc: "slowly regenerates health over time",
                    healthRegen: 3
                }, {
                    id: 9,
                    name: "Miners Helmet",
                    price: 5000,
                    scale: 120,
                    desc: "earn 1 extra gold per resource",
                    extraGold: 1
                }, {
                    id: 32,
                    name: "Musketeer Hat",
                    price: 5000,
                    scale: 120,
                    desc: "reduces cost of projectiles",
                    projCost: 0.5
                }, {
                    id: 7,
                    name: "Bull Helmet",
                    price: 6000,
                    scale: 120,
                    desc: "increases damage done but drains health",
                    healthRegen: -5,
                    dmgMultO: 1.5,
                    spdMult: 0.96
                }, {
                    id: 22,
                    name: "Emp Helmet",
                    price: 6000,
                    scale: 120,
                    desc: "turrets won't attack but you move slower",
                    antiTurret: 1,
                    spdMult: 0.7
                }, {
                    id: 12,
                    name: "Booster Hat",
                    price: 6000,
                    scale: 120,
                    desc: "increases your movement speed",
                    spdMult: 1.16
                }, {
                    id: 26,
                    name: "Barbarian Armor",
                    price: 8000,
                    scale: 120,
                    desc: "knocks back enemies that attack you",
                    dmgK: 0.6
                }, {
                    id: 21,
                    name: "Plague Mask",
                    price: 10000,
                    scale: 120,
                    desc: "melee attacks deal poison damage",
                    poisonDmg: 5,
                    poisonTime: 6
                }, {
                    id: 46,
                    name: "Bull Mask",
                    price: 10000,
                    scale: 120,
                    desc: "bulls won't target you unless you attack them",
                    bullRepel: 1
                }, {
                    id: 14,
                    name: "Windmill Hat",
                    topSprite: true,
                    price: 10000,
                    scale: 120,
                    desc: "generates points while worn",
                    pps: 1.5
                }, {
                    id: 11,
                    name: "Spike Gear",
                    topSprite: true,
                    price: 10000,
                    scale: 120,
                    desc: "deal damage to players that damage you",
                    dmg: 0.45
                }, {
                    id: 53,
                    name: "Turret Gear",
                    topSprite: true,
                    price: 10000,
                    scale: 120,
                    desc: "you become a walking turret",
                    turret: {
                        proj: 1,
                        range: 700,
                        rate: 2500
                    },
                    spdMult: 0.7
                }, {
                    id: 20,
                    name: "Samurai Armor",
                    price: 12000,
                    scale: 120,
                    desc: "increased attack speed and fire rate",
                    atkSpd: 0.78
                }, {
                    id: 58,
                    name: "Dark Knight",
                    price: 12000,
                    scale: 120,
                    desc: "restores health when you deal damage",
                    healD: 0.4
                }, {
                    id: 27,
                    name: "Scavenger Gear",
                    price: 15000,
                    scale: 120,
                    desc: "earn double points for each kill",
                    kScrM: 2
                }, {
                    id: 40,
                    name: "Tank Gear",
                    price: 15000,
                    scale: 120,
                    desc: "increased damage to buildings but slower movement",
                    spdMult: 0.3,
                    bDmg: 3.3
                }, {
                    id: 52,
                    name: "Thief Gear",
                    price: 15000,
                    scale: 120,
                    desc: "steal half of a players gold when you kill them",
                    goldSteal: 0.5
                }, {
                    id: 55,
                    name: "Bloodthirster",
                    price: 20000,
                    scale: 120,
                    desc: "Restore Health when dealing damage. And increased damage",
                    healD: 0.25,
                    dmgMultO: 1.2,
                }, {
                    id: 56,
                    name: "Assassin Gear",
                    price: 20000,
                    scale: 120,
                    desc: "Go invisible when not moving. Can't eat. Increased speed",
                    noEat: true,
                    spdMult: 1.1,
                    invisTimer: 1000
                }];

                // STORE ACCESSORIES:
                this.accessories = [{
                    id: 12,
                    name: "Snowball",
                    price: 1000,
                    scale: 105,
                    xOff: 18,
                    desc: "no effect"
                }, {
                    id: 9,
                    name: "Tree Cape",
                    price: 1000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 10,
                    name: "Stone Cape",
                    price: 1000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 3,
                    name: "Cookie Cape",
                    price: 1500,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 8,
                    name: "Cow Cape",
                    price: 2000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 11,
                    name: "Monkey Tail",
                    price: 2000,
                    scale: 97,
                    xOff: 25,
                    desc: "Super speed but reduced damage",
                    spdMult: 1.35,
                    dmgMultO: 0.2
                }, {
                    id: 17,
                    name: "Apple Basket",
                    price: 3000,
                    scale: 80,
                    xOff: 12,
                    desc: "slowly regenerates health over time",
                    healthRegen: 1
                }, {
                    id: 6,
                    name: "Winter Cape",
                    price: 3000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 4,
                    name: "Skull Cape",
                    price: 4000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 5,
                    name: "Dash Cape",
                    price: 5000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 2,
                    name: "Dragon Cape",
                    price: 6000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 1,
                    name: "Super Cape",
                    price: 8000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 7,
                    name: "Troll Cape",
                    price: 8000,
                    scale: 90,
                    desc: "no effect"
                }, {
                    id: 14,
                    name: "Thorns",
                    price: 10000,
                    scale: 115,
                    xOff: 20,
                    desc: "no effect"
                }, {
                    id: 15,
                    name: "Blockades",
                    price: 10000,
                    scale: 95,
                    xOff: 15,
                    desc: "no effect"
                }, {
                    id: 20,
                    name: "Devils Tail",
                    price: 10000,
                    scale: 95,
                    xOff: 20,
                    desc: "no effect"
                }, {
                    id: 16,
                    name: "Sawblade",
                    price: 12000,
                    scale: 90,
                    spin: true,
                    xOff: 0,
                    desc: "deal damage to players that damage you",
                    dmg: 0.15
                }, {
                    id: 13,
                    name: "Angel Wings",
                    price: 15000,
                    scale: 138,
                    xOff: 22,
                    desc: "slowly regenerates health over time",
                    healthRegen: 3
                }, {
                    id: 19,
                    name: "Shadow Wings",
                    price: 15000,
                    scale: 138,
                    xOff: 22,
                    desc: "increased movement speed",
                    spdMult: 1.1
                }, {
                    id: 18,
                    name: "Blood Wings",
                    price: 20000,
                    scale: 178,
                    xOff: 26,
                    desc: "restores health when you deal damage",
                    healD: 0.2
                }, {
                    id: 21,
                    name: "Corrupt X Wings",
                    price: 20000,
                    scale: 178,
                    xOff: 26,
                    desc: "deal damage to players that damage you",
                    dmg: 0.25
                }];
            }
        };
        class ProjectileManager {
            constructor(Projectile, projectiles, players, ais, objectManager, items, config, utils, server) {
                this.addProjectile = function (x, y, dir, range, speed, indx, owner, ignoreObj, layer, inWindow) {
                    let tmpData = items.projectiles[indx];
                    let tmpProj;
                    for (let i = 0; i < projectiles.length; ++i) {
                        if (!projectiles[i].active) {
                            tmpProj = projectiles[i];
                            break;
                        }
                    }
                    if (!tmpProj) {
                        tmpProj = new Projectile(players, ais, objectManager, items, config, utils, server);
                        tmpProj.sid = projectiles.length;
                        projectiles.push(tmpProj);
                    }
                    tmpProj.init(indx, x, y, dir, speed, tmpData.dmg, range, tmpData.scale, owner);
                    tmpProj.ignoreObj = ignoreObj;
                    tmpProj.layer = layer || tmpData.layer;
                    tmpProj.inWindow = inWindow;
                    tmpProj.src = tmpData.src;
                    return tmpProj;
                };
            }
        };
        class AiManager {

            // AI MANAGER:
            constructor(ais, AI, players, items, objectManager, config, utils, scoreCallback, server) {

                // AI TYPES:
                this.aiTypes = [{
                    id: 0,
                    src: "cow_1",
                    killScore: 150,
                    health: 500,
                    weightM: 0.8,
                    speed: 0.00095,
                    turnSpeed: 0.001,
                    scale: 72,
                    drop: ["food", 50]
                }, {
                    id: 1,
                    src: "pig_1",
                    killScore: 200,
                    health: 800,
                    weightM: 0.6,
                    speed: 0.00085,
                    turnSpeed: 0.001,
                    scale: 72,
                    drop: ["food", 80]
                }, {
                    id: 2,
                    name: "Bull",
                    src: "bull_2",
                    hostile: true,
                    dmg: 20,
                    killScore: 1000,
                    health: 1800,
                    weightM: 0.5,
                    speed: 0.00094,
                    turnSpeed: 0.00074,
                    scale: 78,
                    viewRange: 800,
                    chargePlayer: true,
                    drop: ["food", 100]
                }, {
                    id: 3,
                    name: "Bully",
                    src: "bull_1",
                    hostile: true,
                    dmg: 20,
                    killScore: 2000,
                    health: 2800,
                    weightM: 0.45,
                    speed: 0.001,
                    turnSpeed: 0.0008,
                    scale: 90,
                    viewRange: 900,
                    chargePlayer: true,
                    drop: ["food", 400]
                }, {
                    id: 4,
                    name: "Wolf",
                    src: "wolf_1",
                    hostile: true,
                    dmg: 8,
                    killScore: 500,
                    health: 300,
                    weightM: 0.45,
                    speed: 0.001,
                    turnSpeed: 0.002,
                    scale: 84,
                    viewRange: 800,
                    chargePlayer: true,
                    drop: ["food", 200]
                }, {
                    id: 5,
                    name: "Quack",
                    src: "chicken_1",
                    dmg: 8,
                    killScore: 2000,
                    noTrap: true,
                    health: 300,
                    weightM: 0.2,
                    speed: 0.0018,
                    turnSpeed: 0.006,
                    scale: 70,
                    drop: ["food", 100]
                }, {
                    id: 6,
                    name: "MOOSTAFA",
                    nameScale: 50,
                    src: "enemy",
                    hostile: true,
                    dontRun: true,
                    fixedSpawn: true,
                    spawnDelay: 60000,
                    noTrap: true,
                    colDmg: 100,
                    dmg: 40,
                    killScore: 8000,
                    health: 18000,
                    weightM: 0.4,
                    speed: 0.0007,
                    turnSpeed: 0.01,
                    scale: 80,
                    spriteMlt: 1.8,
                    leapForce: 0.9,
                    viewRange: 1000,
                    hitRange: 210,
                    hitDelay: 1000,
                    chargePlayer: true,
                    drop: ["food", 100]
                }, {
                    id: 7,
                    name: "Treasure",
                    hostile: true,
                    nameScale: 35,
                    src: "crate_1",
                    fixedSpawn: true,
                    spawnDelay: 120000,
                    colDmg: 200,
                    killScore: 5000,
                    health: 20000,
                    weightM: 0.1,
                    speed: 0.0,
                    turnSpeed: 0.0,
                    scale: 70,
                    spriteMlt: 1.0
                }, {
                    id: 8,
                    name: "MOOFIE",
                    src: "wolf_2",
                    hostile: true,
                    fixedSpawn: true,
                    dontRun: true,
                    hitScare: 4,
                    spawnDelay: 30000,
                    noTrap: true,
                    nameScale: 35,
                    dmg: 10,
                    colDmg: 100,
                    killScore: 3000,
                    health: 7000,
                    weightM: 0.45,
                    speed: 0.0015,
                    turnSpeed: 0.002,
                    scale: 90,
                    viewRange: 800,
                    chargePlayer: true,
                    drop: ["food", 1000]
                }, {
                    id: 9,
                    name: "??MOOFIE",
                    src: "wolf_2",
                    hostile: true,
                    fixedSpawn: true,
                    dontRun: true,
                    hitScare: 50,
                    spawnDelay: 60000,
                    noTrap: true,
                    nameScale: 35,
                    dmg: 12,
                    colDmg: 100,
                    killScore: 3000,
                    health: 9000,
                    weightM: 0.45,
                    speed: 0.0015,
                    turnSpeed: 0.0025,
                    scale: 94,
                    viewRange: 1440,
                    chargePlayer: true,
                    drop: ["food", 3000]
                }, {
                    id: 10,
                    name: "??Wolf",
                    src: "wolf_1",
                    hostile: true,
                    fixedSpawn: true,
                    dontRun: true,
                    hitScare: 50,
                    spawnDelay: 30000,
                    nameScale: 35,
                    dmg: 10,
                    killScore: 700,
                    health: 500,
                    weightM: 0.45,
                    speed: 0.00115,
                    turnSpeed: 0.0025,
                    scale: 88,
                    viewRange: 1440,
                    chargePlayer: true,
                    drop: ["food", 400]
                }, {
                    id: 11,
                    name: "??Bully",
                    src: "bull_1",
                    hostile: true,
                    fixedSpawn: true,
                    dontRun: true,
                    hitScare: 50,
                    spawnDelay: 100000,
                    nameScale: 35,
                    dmg: 20,
                    killScore: 5000,
                    health: 5000,
                    weightM: 0.45,
                    speed: 0.0015,
                    turnSpeed: 0.0025,
                    scale: 94,
                    viewRange: 1440,
                    chargePlayer: true,
                    drop: ["food", 800]
                }];

                // SPAWN AI:
                this.spawn = function (x, y, dir, index) {
                    let tmpObj = ais.find((tmp) => !tmp.active);
                    if (!tmpObj) {
                        tmpObj = new AI(ais.length, objectManager, players, items, utils, config, scoreCallback, server);
                        ais.push(tmpObj);
                    }
                    tmpObj.init(x, y, dir, index, this.aiTypes[index]);
                    return tmpObj;
                };
            }

        };
        class AI {
            constructor(sid, objectManager, players, items, utils, config, scoreCallback, server) {
                this.sid = sid;
                this.isAI = true;
                this.nameIndex = utils.randInt(0, config.cowNames.length - 1);

                // INIT:
                this.init = function (x, y, dir, index, data) {
                    this.x = x;
                    this.y = y;
                    this.startX = data.fixedSpawn ? x : null;
                    this.startY = data.fixedSpawn ? y : null;
                    this.xVel = 0;
                    this.yVel = 0;
                    this.zIndex = 0;
                    this.dir = dir;
                    this.dirPlus = 0;
                    this.index = index;
                    this.src = data.src;
                    if (data.name) this.name = data.name;
                    this.weightM = data.weightM;
                    this.speed = data.speed;
                    this.killScore = data.killScore;
                    this.turnSpeed = data.turnSpeed;
                    this.scale = data.scale;
                    this.maxHealth = data.health;
                    this.leapForce = data.leapForce;
                    this.health = this.maxHealth;
                    this.chargePlayer = data.chargePlayer;
                    this.viewRange = data.viewRange;
                    this.drop = data.drop;
                    this.dmg = data.dmg;
                    this.hostile = data.hostile;
                    this.dontRun = data.dontRun;
                    this.hitRange = data.hitRange;
                    this.hitDelay = data.hitDelay;
                    this.hitScare = data.hitScare;
                    this.spriteMlt = data.spriteMlt;
                    this.nameScale = data.nameScale;
                    this.colDmg = data.colDmg;
                    this.noTrap = data.noTrap;
                    this.spawnDelay = data.spawnDelay;
                    this.hitWait = 0;
                    this.waitCount = 1000;
                    this.moveCount = 0;
                    this.targetDir = 0;
                    this.active = true;
                    this.alive = true;
                    this.runFrom = null;
                    this.chargeTarget = null;
                    this.dmgOverTime = {};
                };

                let tmpRatio = 0;
                let animIndex = 0;
                this.animate = function (delta) {
                    if (this.animTime > 0) {
                        this.animTime -= delta;
                        if (this.animTime <= 0) {
                            this.animTime = 0;
                            this.dirPlus = 0;
                            tmpRatio = 0;
                            animIndex = 0;
                        } else {
                            if (animIndex == 0) {
                                tmpRatio += delta / (this.animSpeed * config.hitReturnRatio);
                                this.dirPlus = utils.lerp(0, this.targetAngle, Math.min(1, tmpRatio));
                                if (tmpRatio >= 1) {
                                    tmpRatio = 1;
                                    animIndex = 1;
                                }
                            } else {
                                tmpRatio -= delta / (this.animSpeed * (1 - config.hitReturnRatio));
                                this.dirPlus = utils.lerp(0, this.targetAngle, Math.max(0, tmpRatio));
                            }
                        }
                    }
                };

                // ANIMATION:
                this.startAnim = function () {
                    this.animTime = this.animSpeed = 600;
                    this.targetAngle = Math.PI * 0.8;
                    tmpRatio = 0;
                    animIndex = 0;
                };

            };

        };
        class Player {
            constructor(id, sid, config, utils, projectileManager, objectManager, players, ais, items, hats, accessories, server, scoreCallback, iconCallback) {
                this.id = id;
                this.sid = sid;
                this.tmpScore = 0;
                this.team = null;
                this.latestSkin = 0;
                this.oldSkinIndex = 0;
                this.skinIndex = 0;
                this.latestTail = 0;
                this.oldTailIndex = 0;
                this.tailIndex = 0;
                this.hitTime = 0;
                this.lastHit = 0;
                this.tails = {};
                for (let i = 0; i < accessories.length; ++i) {
                    if (accessories[i].price <= 0) {
                        this.tails[accessories[i].id] = 1;
                    }
                }
                this.skins = {};
                for (let i = 0; i < hats.length; ++i) {
                    if (hats[i].price <= 0) {
                        this.skins[hats[i].id] = 1;
                    }
                }
                this.points = 0;
                this.dt = 0;
                this.hidden = false;
                this.itemCounts = {};
                this.isPlayer = true;
                this.pps = 0;
                this.moveDir = undefined;
                this.skinRot = 0;
                this.lastPing = 0;
                this.iconIndex = 0;
                this.skinColor = 0;
                this.dist2 = 0;
                this.aim2 = 0;
                this.maxSpeed = 1;
                this.chat = {
                    message: null,
                    count: 0
                };
                this.backupNobull = true;
                this.circle = false;
                this.circleRad = 200;
                this.circleRadSpd = 0.1;
                this.cAngle = 0;

                // SPAWN:
                this.spawn = function (moofoll) {
                    this.attacked = false;
                    this.death = false;
                    this.spinDir = 0;
                    this.sync = false;
                    this.antiBull = 0;
                    this.bullTimer = 0;
                    this.poisonTimer = 0;
                    this.active = true;
                    this.alive = true;
                    this.lockMove = false;
                    this.lockDir = false;
                    this.minimapCounter = 0;
                    this.chatCountdown = 0;
                    this.shameCount = 0;
                    this.shameTimer = 0;
                    this.sentTo = {};
                    this.gathering = 0;
                    this.gatherIndex = 0;
                    this.shooting = {};
                    this.shootIndex = 9;
                    this.autoGather = 0;
                    this.animTime = 0;
                    this.animSpeed = 0;
                    this.mouseState = 0;
                    this.buildIndex = -1;
                    this.weaponIndex = 0;
                    this.weaponCode = 0;
                    this.weaponVariant = 0;
                    this.primaryIndex = undefined;
                    this.secondaryIndex = undefined;
                    this.dmgOverTime = {};
                    this.noMovTimer = 0;
                    this.maxXP = 300;
                    this.XP = 0;
                    this.age = 1;
                    this.kills = 0;
                    this.upgrAge = 2;
                    this.upgradePoints = 0;
                    this.x = 0;
                    this.y = 0;
                    this.oldXY = {
                        x: 0,
                        y: 0
                    };
                    this.zIndex = 0;
                    this.xVel = 0;
                    this.yVel = 0;
                    this.slowMult = 1;
                    this.dir = 0;
                    this.dirPlus = 0;
                    this.targetDir = 0;
                    this.targetAngle = 0;
                    this.maxHealth = 100;
                    this.health = this.maxHealth;
                    this.oldHealth = this.maxHealth;
                    this.damaged = 0;
                    this.scale = config.playerScale;
                    this.speed = config.playerSpeed;
                    this.resetMoveDir();
                    this.resetResources(moofoll);
                    this.items = [0, 3, 6, 10];
                    this.weapons = [0];
                    this.shootCount = 0;
                    this.weaponXP = [];
                    this.reloads = {
                        0: 0,
                        1: 0,
                        2: 0,
                        3: 0,
                        4: 0,
                        5: 0,
                        6: 0,
                        7: 0,
                        8: 0,
                        9: 0,
                        10: 0,
                        11: 0,
                        12: 0,
                        13: 0,
                        14: 0,
                        15: 0,
                        53: 0,
                    };
                    this.bowThreat = {
                        9: 0,
                        12: 0,
                        13: 0,
                        15: 0,
                    };
                    this.damageThreat = 0;
                    this.inTrap = false;
                    this.canEmpAnti = false;
                    this.empAnti = false;
                    this.soldierAnti = false;
                    this.poisonTick = 0;
                    this.bullTick = 0;
                    this.setPoisonTick = false;
                    this.setBullTick = false;
                    this.antiTimer = 2;
                };

                // RESET MOVE DIR:
                this.resetMoveDir = function () {
                    this.moveDir = undefined;
                };

                // RESET RESOURCES:
                this.resetResources = function (moofoll) {
                    for (let i = 0; i < config.resourceTypes.length; ++i) {
                        this[config.resourceTypes[i]] = moofoll ? 100 : 0;
                    }
                };

                // ADD ITEM:
                this.getItemType = function(id) {
                    let findindx = this.items.findIndex((ids) => ids == id);
                    if (findindx != -1) {
                        return findindx;
                    } else {
                        return items.checkItem.index(id, this.items);
                    }
                };

                // SET DATA:
                this.setData = function (data) {
                    this.id = data[0];
                    this.sid = data[1];
                    this.name = data[2];
                    this.x = data[3];
                    this.y = data[4];
                    this.dir = data[5];
                    this.health = data[6];
                    this.maxHealth = data[7];
                    this.scale = data[8];
                    this.skinColor = data[9];
                };

                // UPDATE POISON TICK:
                this.updateTimer = function() {

                    this.bullTimer -= 1;
                    if (this.bullTimer <= 0) {
                        this.setBullTick = false;
                        this.bullTick = game.tick - 1;
                        this.bullTimer = config.serverUpdateRate;
                    }
                    this.poisonTimer -= 1;
                    if (this.poisonTimer <= 0) {
                        this.setPoisonTick = false;
                        this.poisonTick = game.tick - 1;
                        this.poisonTimer = config.serverUpdateRate;
                    }

                };
                this.update = function(delta) {
                    if (this.active) {

                        // MOVE:
                        let gear = {
                            skin: findID(hats, this.skinIndex),
                            tail: findID(accessories, this.tailIndex)
                        }
                        let spdMult = ((this.buildIndex >= 0) ? 0.5 : 1) * (items.weapons[this.weaponIndex].spdMult || 1) * (gear.skin ? (gear.skin.spdMult || 1) : 1) * (gear.tail ? (gear.tail.spdMult || 1) : 1) * (this.y <= config.snowBiomeTop ? ((gear.skin && gear.skin.coldM) ? 1 : config.snowSpeed) : 1) * this.slowMult;
                        this.maxSpeed = spdMult;

                    }
                };

                let tmpRatio = 0;
                let animIndex = 0;
                this.animate = function(delta) {
                    if (this.animTime > 0) {
                        this.animTime -= delta;
                        if (this.animTime <= 0) {
                            this.animTime = 0;
                            this.dirPlus = 0;
                            tmpRatio = 0;
                            animIndex = 0;
                        } else {
                            if (animIndex == 0) {
                                tmpRatio += delta / (this.animSpeed * config.hitReturnRatio);
                                this.dirPlus = utils.lerp(0, this.targetAngle, Math.min(1, tmpRatio));
                                if (tmpRatio >= 1) {
                                    tmpRatio = 1;
                                    animIndex = 1;
                                }
                            } else {
                                tmpRatio -= delta / (this.animSpeed * (1-config.hitReturnRatio));
                                this.dirPlus = utils.lerp(0, this.targetAngle, Math.max(0, tmpRatio));
                            }
                        }
                    }
                };

                // GATHER ANIMATION:
                this.startAnim = function (didHit, index) {
                    this.animTime = this.animSpeed = items.weapons[index].speed;
                    this.targetAngle = (didHit ? -config.hitAngle : -Math.PI);
                    tmpRatio = 0;
                    animIndex = 0;
                };

                // CAN SEE:
                this.canSee = function(other) {
                    if (!other) return false;
                    let dx = Math.abs(other.x - this.x) - other.scale;
                    let dy = Math.abs(other.y - this.y) - other.scale;
                    return dx <= (config.maxScreenWidth / 2) * 1.3 && dy <= (config.maxScreenHeight / 2) * 1.3;
                };

                // SHAME SYSTEM:
                this.judgeShame = function () {
                    if (this.oldHealth < this.health) {
                        if (this.hitTime) {
                            let timeSinceHit = game.tick - this.hitTime;
                            this.lastHit = game.tick;
                            this.hitTime = 0;
                            if (timeSinceHit < 2) {
                                this.shameCount++;
                            } else {
                                this.shameCount = Math.max(0, this.shameCount - 2);
                            }
                        }
                    } else if (this.oldHealth > this.health) {
                        this.hitTime = game.tick;
                    }
                };
                this.addShameTimer = function () {
                    this.shameCount = 0;
                    this.shameTimer = 30;
                    let interval = setInterval(() => {
                        this.shameTimer--;
                        if (this.shameTimer <= 0) {
                            clearInterval(interval);
                        }
                    }, 1000);
                };

                // CHECK TEAM:
                this.isTeam = function (tmpObj) {
                    return (this == tmpObj || (this.team && this.team == tmpObj.team));
                };

                // FOR THE PLAYER:
                this.findAllianceBySid = function (sid) {
                    return this.team ? alliancePlayers.find((THIS) => THIS === sid) : null;
                };
                // UPDATE WEAPON RELOAD:
                this.manageReload = function () {
                    if (this.shooting[53]) {
                        this.shooting[53] = 0;
                        this.reloads[53] = (2500 - game.tickRate);
                    } else {
                        if (this.reloads[53] > 0) {
                            this.reloads[53] = Math.max(0, this.reloads[53] - game.tickRate);
                        }
                    }
                    if (this.gathering || this.shooting[1]) {
                        if (this.gathering) {
                            this.gathering = 0;
                            this.reloads[this.gatherIndex] = (items.weapons[this.gatherIndex].speed * (this.skinIndex == 20 ? 0.78 : 1));
                            this.attacked = true;
                        }
                        if (this.shooting[1]) {
                            this.shooting[1] = 0;
                            this.reloads[this.shootIndex] = (items.weapons[this.shootIndex].speed * (this.skinIndex == 20 ? 0.78 : 1));
                            this.attacked = true;
                        }
                    } else {
                        this.attacked = false;
                        if (this.buildIndex < 0) {
                            if (this.reloads[this.weaponIndex] > 0) {
                                this.reloads[this.weaponIndex] = Math.max(0, this.reloads[this.weaponIndex] - game.tickRate);

                                if (this.reloads[this.primaryIndex] == 0 && this.reloads[this.weaponIndex] == 0) {
                                    this.antiBull++;
                                    game.tickBase(() => {
                                        this.antiBull = 0;
                                    }, 1);
                                }
                            }
                        }
                    }
                };
            }
        };

        function selectToBuild(index, wpn) {
            soc.send("z", index, wpn);
        }

        function selectWeapon(index, isPlace) {
            if (!isPlace) {
                player.weaponCode = index;
            }
            soc.send("z", index, 1);
        }

        function sendAutoGather() {
            soc.send("K", 1, 1);
        }

        function sendAtck(id, angle) {
            soc.send("F", id, angle, 1);
        }

        function place(id, rad, rmd) {
            try {
                if (id == undefined) return;
                let item = items.list[player.items[id]];
                let tmpS = player.scale + item.scale + (item.placeOffset || 0);
                let tmpX = player.x2 + tmpS * Math.cos(rad);
                let tmpY = player.y2 + tmpS * Math.sin(rad);
                if (id === 0 || (player.alive && inGame && player.itemCounts[item.group.id] == undefined ? true : player.itemCounts[item.group.id] < (config.isSandbox ? id === 3 || id === 5 ? 299 : 99 : item.group.limit ? item.group.limit : 99))) {
                    selectToBuild(player.items[id]);
                    sendAtck(1, rad);
                    selectWeapon(player.weaponCode, 1);
                    if (id > 1) {
                        placeVisible.push({
                            x: tmpX,
                            y: tmpY,
                            name: item.name,
                            scale: item.scale,
                            dir: rad
                        });
                        game.tickBase(() => {
                            placeVisible.shift();
                        }, 1)
                    }
                }
            } catch (e) { }
        }
        function checkPlace(id, rad) {
            try {
                if (id == undefined) return;
                let item = items.list[player.items[id]];
                let tmpS = player.scale + item.scale + (item.placeOffset || 0);
                let tmpX = player.x2 + tmpS * Math.cos(rad);
                let tmpY = player.y2 + tmpS * Math.sin(rad);
                if (objectManager.checkItemLocation(tmpX, tmpY, item.scale, 0.6, item.id, false, player)) {
                    place(id, rad, 1);
                }
            } catch (e) {}
        }

        // jumpscare code warn
        let tmpList = [];

        let objectManager = new Objectmanager(GameObject, gameObjects, utils, config);
        let store = new Store();
        let hats = store.hats;
        let accessories = store.accessories;
        let projectileManager = new ProjectileManager(Projectile, projectiles, players, ais, objectManager, items, config, utils);
        let aiManager = new AiManager(ais, AI, players, items, null, config, utils);



        let lastDeath;
        let minimapData;
        let mapMarker = {};
        let mapPings = [];
        let tmpPing;

        let breakTrackers = [];

        let runAtNextTick = [];



        // SHOW ITEM INFO:
        function showItemInfo(item, isWeapon, isStoreItem) {
            if (player && item) {
                utils.removeAllChildren(itemInfoHolder);
                itemInfoHolder.classList.add("visible");
                utils.generateElement({
                    id: "itemInfoName",
                    text: utils.capitalizeFirst(item.name),
                    parent: itemInfoHolder
                });
                utils.generateElement({
                    id: "itemInfoDesc",
                    text: item.desc,
                    parent: itemInfoHolder
                });
                if (isStoreItem) {

                } else if (isWeapon) {
                    utils.generateElement({
                        class: "itemInfoReq",
                        text: !item.type?"primary":"secondary",
                        parent: itemInfoHolder
                    });
                } else {
                    for (let i = 0; i < item.req.length; i += 2) {
                        utils.generateElement({
                            class: "itemInfoReq",
                            html: item.req[i] + "<span class='itemInfoReqVal'> x" + item.req[i + 1] + "</span>",
                            parent: itemInfoHolder
                        });
                    }
                    if (item.group.limit) {
                        utils.generateElement({
                            class: "itemInfoLmt",
                            text: (player.itemCounts[item.group.id] || 0) + "/" + (config.isSandbox ? 99 : item.group.limit),
                            parent: itemInfoHolder
                        });
                    }
                }
            } else {
                itemInfoHolder.classList.remove("visible");
            }
        }

        // RESIZE:
        const resize = () => {
            sW = window.innerWidth;
            sH = window.innerHeight;
            let scaleFillNative = Math.max(sW / screen.max.width, sH / screen.max.height) * pixelDensity;
            screen.current.width = sW * window.devicePixelRatio;
            screen.current.height = sH * window.devicePixelRatio;

            canvas.style.width = sW + "px";
            canvas.style.height = sH + "px";

            canvas.width = screen.current.width;
            canvas.height = screen.current.height;
            ctx.setTransform(
                scaleFillNative, 0,
                0, scaleFillNative,
                (sW * pixelDensity - (screen.max.width * scaleFillNative)) / 2,
                (sH * pixelDensity - (screen.max.height * scaleFillNative)) / 2
            );
        };
        window.addEventListener("resize", resize);
        resize();

        // INPUT utils:
        function getMoveDir() {
            let dx = 0;
            let dy = 0;
            for (let key in moveKeys) {
                let tmpDir = moveKeys[key];
                dx += !!keys[key] * tmpDir[0];
                dy += !!keys[key] * tmpDir[1];
            }
            return dx == 0 && dy == 0 ? undefined : Math.atan2(dy, dx);
        }

        function getAttackDir(debug) {
            if (!player || !inGame) return;
            lastDir = math[16](mouseY - (sH / 2), mouseX - (sW / 2));
            return lastDir;
        }


        // KEYS:
        function keysActive() {
            return (allianceMenu.style.display != "block" &&
                    chatHolder.style.display != "block");
        }

        var followMe = false;
        function keyDown(event) {
            let keyNum = event.which || event.keyCode || 0;
            if (player && player.alive && keysActive()) {
                if (!keys[keyNum]) {
                    keys[keyNum] = 1;
                    macro[event.key] = 1;
                    if (keyNum == 69) {
                        soc.send("K", getAttackDir())
                    } else if (keyNum == 67) {
                        updateMapMarker();
                    } else if (player.weapons[keyNum - 49] != undefined) {
                        player.weaponCode = player.weapons[keyNum - 49];
                    } else if (moveKeys[keyNum]) {
                        sendMoveDir();
                    } else if (event.key == "z") {
                        mills.place = !mills.place;
                    } else if (event.key == "y") {
                        for (let i = 0; i < 5; i++) {
                            botSpawner();
                        }
                    } else if (keyNum == 32) {
                        soc.send("F", 1, getAttackDir(), 1);
                        soc.send("F", 0, getAttackDir(), 1);
                    } else if (event.key == "o") {
                        followMe = !followMe
                        showText(player.x, player.y, followMe);
                    } else if (event.key == ",") {
                        showText(player.x, player.y, "Sync!!");
                        botK.sync.toggle = !botK.sync.toggle;
                    }
                }
            }
        }
        const keyUp = (event) => {
            let keyNum = event.which || event.keyCode || 0;
            if (keysActive()) {
                if (keys[keyNum]) {
                    keys[keyNum] = 0;
                    macro[event.key] = 0;
                    if (moveKeys[keyNum]) {
                        sendMoveDir();
                    }
                }
            }
        }

        window.onkeydown = keyDown;
        window.onkeyup = keyUp;

        window.addEventListener("keyup", window.onkeyup);
        window.addEventListener("keydown", window.onkeydown);


        //Get Url

        //Class
        class botK {
            constructor(id, sid, hats, accessories, sendWS) {
              this.botName = Math.random() < 0.5 ? "YB" : "HB";
                this.inGame = false;
                this.alive = false;

                this.id = id;
                this.sid = sid;

                this.dir = 0;
                this.ndir = 0;
                this.lockDir = false;

                //For syncing
                this.target = [];
                this.sync = {
                    toggled: false,
                    canInsta: false,
                }

                //For Insta user
                this.insta = function(type) {
                    if (type == "normal") {
                    } else if (type == "reverse") {
                    }
                };

                //Heal momint
                this.health = 0;
                this.maxHealth = 100;
                this.oldHealth = this.health;

                this.shameCount = 0;
                this.dangerShame = 7;
                this.HitTime = Date.now();
                this.time = 0;

                //X and Y
                this.inRiver = this.y2 >= config.mapScale / 2 - config.riverWidth / 2 && this.y2 <= config.mapScale / 2 + config.riverWidth / 2
                this.x = 0;
                this.y = 0;

                this.x2 = 0;
                this.y2 = 0;

                this.old = {
                    x2: 0,
                    y2: 0,
                }

                this.xVel = 0;
                this.yVel = 0;

                // Resource Check;
                this.wood = 0;
                this.stone = 0;
                this.food = 0;
                this.points = 0;

                // Hat or other stuff
                this.latestSkin = 0;
                this.latestTail = 0;

                this.skinIndex = 0;
                this.tailIndex = 0;

                this.skins = {};
                this.tails = {};
                for (let i = 0; i < accessories.length; ++i) {
                    if (accessories[i].price <= 0) {
                        this.tails[accessories[i].id] = 1;
                    }
                }
                for (let i = 0; i < hats.length; ++i) {
                    if (hats[i].price <= 0) {
                        this.skins[hats[i].id] = 1;
                    }
                }

                //For Items && weapons
                this.ItemCounts = {};
                this.Items = [0, 3, 6, 10];
                this.items = [];
                this.millCount = 0;
                this.maxMill = 298;


                this.reloading = false;
                this.shooting = {};
                this.reloadTime = 0;
                this.reloads = {
                    wep: {
                        pri: 0,
                        sec: 0,
                    },
                    hat: {
                        turret: 0,
                    }
                };

                this.weaponIndex = 0;
                this.weapons = [];
                this.primary;
                this.secondary;






                // Others
                this.serverWait = async function(time) {
                      return new Promise((resolve) => setTimeout(resolve, time));
                }
                this.closeWeb = function(WebSocket) {
                    console.warn("Closing")
                    return WebSocket.close();
                };
            }
        }
        function botSpawner() {
            let urs = soc.socket.url || soc.socket.href;
            let token = urs.split("token=")[1];
            if (!token) return;
            let endIdx = token.indexOf("&");
            if (endIdx !== -1) {
                token = token.substring(0, endIdx);
            }
            token = decodeURIComponent(token);
            if (token.startsWith("alt:")) {
                token = token.substring(4);
            } else if (token.startsWith("alt%3A")) {
                token = token.substring(6);
            }
            let url = ws.url.split("token=")[0] + "token=" + encodeURIComponent("alt:" + token);
            let btc = new WebSocket(url);
            btc.emit = (type, arg1, arg2, arg3) => {
                btc.send(window.msgpack.encode([type, [arg1, arg2, arg3]]));
            };

            btc.sid = null;
            btc.wood = 0;
            btc.points = 0;
            btc.Items = [0, 3, 6, 10];
            btc.weapons = [0];

            btc.tails = {};
            btc.skins = {};

            btc.points = 0;
            btc.score = 0;
            btc.stone = 0;
            btc.food = 0;

            btc.millcount = 0;
            btc.Time = 0;
            btc.Count = 0;
            btc.health = 100;
            btc.alive = false;

            btc.accessories = [0];
            btc.enemy = [];
            btc.old = {
                x2: 0,
                y2: 0,
            };
            btc.inTrap = false;
            btc.trapData = {
                sid: void 0,
                x: void 0,
                y: void 0
            };
            btc.x2 = 0;
            btc.y2 = 0;
            btc.binaryType = "arraybuffer";
            btc.inGame = false;
            btc.upgraded = 0;

            let snowBiomeTop = 2400;
            let riverWidth = 724;
            let mapScale = 14400;

            //Smart gathering lel;
            btc.Gath = {
                isGathering: false,
                isThrottle: false,
            };

            btc.getDirection = (bx, by, tx, ty) => {
                return (Math.atan2(ty - by, bx - tx) + Math.PI * 2) % (Math.PI * 2);
            };
            function caf(e, t) {
                try {
                    return Math.atan2((t.y2 || t.y) - (e.y2 || e.y), (t.x2 || t.x) - (e.x2 || e.x));
                } catch (e) {
                    return 0;
                }
            }
            function storeEquip(id, index) {
                btc.emit("c", 0, id, index);
            }

            function storeBuy(id, index) {
                btc.emit("c", 1, id, index);
            }

            btc.findPlayer = function(t) {
                for (let e = 0; e < btc.players.length; e++) {
                    if (btc.players[e].sid == t) return btc.players[e]
                }
                return null
            };
            btc.spawn = function() {
                btc.inGame = true;
                btc.emit("M", {
                    name: "wats bots",
                    moofoll: 1,
                    skin: 0
                });
            };
            btc.onopen = (() => {
                btc.spawn();
                botK.wait(111);
            });
            btc.sendAtck = function(id, angle) {
                btc.emit("F", id, angle, 1);
            };
            btc.sendAutoGather = function() {
                if (btc.Gath.isThrottle) return;
                btc.Gath.isThrottle = true;
                btc.Gath.isGathering = !btc.Gath.isGathering;
                btc.emit("K", 1, 1);
                setInterval(btc.Gath.isThrottle = false, 69);
            };
            btc.doUpgrade = function(index) {
                btc.emit("H", index);
            };
            btc.place = function(id, a) {
                btc.emit("z", btc.Items[id]);
                btc.emit("F", 1, a);
                btc.emit("z", btc.weaponIndex, 1);
            };
            btc.placeThreeMills = function() {
                const aim = Math.atan2(btc.old.y2 - btc.y2, btc.old.x2 - btc.x2);
                const offsets = [0, Math.PI / 2.3, -Math.PI / 2.3];
                offsets.forEach(offset => {
                    btc.place(3, aim + offset);
                });
            };
            btc.equip = {
                acc: function(id) {
                    btc.emit("c", 0, id, 1);
                },
                hat: function(id) {
                    btc.emit("c", 0, id, 0);
                }
            }
            btc.buy = {
                acc: function(id) {
                    btc.emit("c", 1, id, 1);
                },
                hat: function(id) {
                    btc.emit("c", 1, id, 0);
                }
            }
            btc.buyEquip = function(id, index) {
                if (index === 0) {
                    btc.buy.hat(id);
                    btc.equip.hat(id);
                } else if (index === 1) {
                    btc.buy.acc(id);
                    btc.equip.acc(id);
                }
            };

            btc.inRiver = btc.y2 >= config.mapScale / 2 - config.riverWidth / 2 && btc.y2 <= config.mapScale / 2 + config.riverWidth / 2
            btc.onmessage = function(message) {
                let decodedData = window.msgpack.decode(new Uint8Array(message.data));
                let data;
                if (decodedData.length > 1) {
                    data = [decodedData[0], ...decodedData[1]];
                    if (Array.isArray(data[1])) {
                        data = data;
                    }
                } else {
                    data = decodedData;
                }
                if (!data) return;

                if (data[0] == "N") {
                    if (data[1] == "wood") btc.wood = data[2];
                    if (data[1] == "food") btc.food = data[2];
                    if (data[1] == "stone") btc.stone = data[2];
                    if (data[1] == "points") btc.points = data[2]
                }
                if (data[0] == "C") {
                    btc.sid = data[1];
                }
                if (data[0] == "D") {
                    if (data[1]) {
                        btc.alive = true;
                        btc.inGame = true;
                        btc.x2 = undefined;
                        btc.y2 = undefined;
                        btc.old = {
                            x2: data[0][3],
                            y2: data[0][4]
                        }
                    }
                }
                if (data[0] == "R") {
                    //btc.players = btc.players.filter(e => e.sid != data[1]);
                }

                if (data[0] == "G") {
                    for (var i = 0; i < data[1].length / 3; i++) {
                        let leaderboardInfo = data[1].slice(3 * i, 3 * i + 3);
                        if (leaderboardInfo[0] == btc.sid) {
                            btc.points = leaderboardInfo[2];
                        }
                    }
                }

                if (data[0] == "P") {
                    setInterval(() => {
                        btc.spawn();
                    }, 111);
                    btc.inGame = false;
                }
                if (data[0] == "S") {
                    if (data[1] == 3) {
                        btc.millcount = data[2];
                    }
                }
                if (data[0] == "U") {
                    if (data[1] > 0) {
                        if (btc.upgraded == 0) {
                            btc.doUpgrade(3);
                        } else if (btc.upgraded == 1) {
                            btc.doUpgrade(17);
                        } else if (btc.upgraded == 2) {
                            btc.doUpgrade(31);
                        } else if (btc.upgraded == 3) {
                            btc.doUpgrade(27);
                        } else if (btc.upgraded == 4) {
                            btc.doUpgrade(9);
                        } else if (btc.upgraded == 5) {
                            btc.doUpgrade(38);
                        } else if (btc.upgraded == 6) {
                            btc.doUpgrade(12);
                        } else if (btc.upgraded == 7) {
                            btc.doUpgrade(15);
                        }
                        btc.upgraded++;
                    }
                }
                if (data[0] == "V") {
                    if (data[1]) {
                        if (data[2]) {
                            btc.weapons = data[1];
                        } else {
                            btc.Items = data[1];
                        }
                    }
                }
                if (data[0] == "14") {
                    let index = data[0];
                    let value = data[1];
                    btc.itemCounts[index] = value;
                }
                if (data[0] == "O" && data[1] == btc.sid) {
                    let dmg = btc.health - data[2];
                    if ((btc.health - data[2]) < 0) {
                        if (btc.Time) {
                            let timeHit = Date.now() - btc.Time;
                            btc.Time = 0;
                            if (timeHit <= 120) {
                                btc.Count++;
                            } else {
                                btc.Count = Math.max(0, btc.Count - 2);
                            }
                        }
                    } else {
                        btc.Time = Date.now();
                    }
                    if (dmg > (btc.skinIndex == 6 ? 45 : 9) && btc.Count < 4) {
                        btc.place(0);
                    } else {
                        setTimeout(() => {
                            btc.place(0);
                        }, 150);
                    }
                    btc.health = data[2];
                }
                if (data == "5") {
                    if (data[3]) {
                        if (!data[1])
                            btc.tails[data[2]] = 1;
                        else
                            btc.tailIndex = data[2];
                    } else {
                        if (!data[1])
                            btc.skins[data[2]] = 1;
                        else
                            btc.skinIndex = data[2];
                    }
                }
                if (data[0] == "a") {
                    let tmpData = data[1];
                    btc.enemy = [];
                    for (let i = 0; i < tmpData.length;) {
                        if (tmpData[i] == btc.sid) {
                            btc.x2 = tmpData[i + 1];
                            btc.y2 = tmpData[i + 2];
                            btc.d2 = tmpData[i + 3];
                            btc.buildIndex = tmpData[i + 4];
                            btc.weaponIndex = tmpData[i + 5];
                            btc.weaponVariant = tmpData[i + 6];
                            btc.team = tmpData[i + 7];
                            btc.isLeader = tmpData[i + 8];
                            btc.skinIndex = tmpData[i + 9];
                            btc.tailIndex = tmpData[i + 10];
                            btc.iconIndex = tmpData[i + 11];
                            btc.zIndex = tmpData[i + 12];
                            btc.visible = true;
                        }
                        i += 13;
                    }
                    if (!(tmpData[i + 0] == btc.sid || tmpData[i + 7] && tmpData[i + 7] == btc.team)) {
                        btc.enemy.push(data[1].slice(13 * i, 13 * i + 13));
                    }
                    if (btc.inGame) {
                        if (player.team && btc.team != player.team) {
                            btc.emit("b", player.team);
                        }
                        if (followMe) { // make follow ong
                            //btc.buyEquip(15, 0);
                            let direction = Math.atan2(player.y2 - btc.y2, player.x2 - btc.x2);
                            let distance = Math.hypot(player.y2 - btc.y2, player.x2 - btc.x2);
                            if (distance > 200) {
                                btc.emit("9", direction);
                                if (btc.millcount <= 296 && window.location.hostname == "sandbox.moomoo.io") {
                                    let aim = Math.atan2(btc.old.y2 - btc.y2, btc.old.x2 - btc.x2);
                                    btc.emit("D", aim);
                                    btc.placeThreeMills();
                                }
                                btc.old = {
                                    x2: btc.x2,
                                    y2: btc.y2
                                };
                                //}
                            } else {
                                btc.emit("9", undefined)
                            }
                        }
                        if (sync.throttle) {
                            sync.throttle = false;
                            if (Math.hypot(player.y2 - btc.y2, player.x2 - btc.x2) < 500) {
                                btc.emit("6", "Boom :>");
                                btc.buyEquip(37,0);
                            }
                        } else {
                            btc.buyEquip(44, 0);
                        }
                    }


                }
            }
        }







        function sendMoveDir() {
            let newMoveDir = getMoveDir();
            if (lastMoveDir == undefined || newMoveDir == undefined || Math.abs(newMoveDir - lastMoveDir) > 0.3) {
                soc.send("9", newMoveDir, 1);
                lastMoveDir = newMoveDir;
            }
        }

        // BUTTON EVENTS:
        function bindEvents() {}
        bindEvents();


        // ITEM COUNT DISPLAY:
        function updateItemCounts(index, value) {
            if (player) {
                player.itemCounts[index] = value;
            }
        }

        // SET INIT DATA:
        function setInitData(data) {
            alliances = data.teams;
        }

        // SETUP GAME:
        function setupGame(yourSID) {
            keys = {};
            macro = {};
            playerSID = yourSID;
            attackState = 0;
            inGame = true;
            soc.send("F", 0, getAttackDir(), 1);
            if (firstSetup) {
                firstSetup = false;
                gameObjects.length = 0;
            }
        }

        // ADD NEW PLAYER:
        function addPlayer(data, isYou) {
            let tmpPlayer = findPlayerByID(data[0]);
            if (!tmpPlayer) {
                tmpPlayer = new Player(data[0], data[1], config, utils, projectileManager,
                                       objectManager, players, ais, items, hats, accessories);
                players.push(tmpPlayer);
            }
            tmpPlayer.spawn(isYou ? true : null);
            tmpPlayer.visible = false;
            tmpPlayer.oldPos = {
                x2: undefined,
                y2: undefined
            };
            tmpPlayer.x2 = undefined;
            tmpPlayer.y2 = undefined;
            tmpPlayer.x3 = undefined;
            tmpPlayer.y3 = undefined;
            tmpPlayer.setData(data);
            if (isYou) {
                if (!player) {
                    window.prepareUI(tmpPlayer);
                }
                player = tmpPlayer;
                camX = player.x;
                camY = player.y;
                updateItems();
                updateAge();
            }
        }

        // REMOVE PLAYER:
        function removePlayer(id) {
            for (let i = 0; i < players.length; i++) {
                if (players[i].id == id) {
                    players.splice(i, 1);
                    break;
                }
            }
        }

        // UPDATE HEALTH:
        function updateHealth(sid, value) {
            tmpObj = findPlayerBySID(sid);
            if (tmpObj) {
                tmpObj.oldHealth = tmpObj.health;
                tmpObj.health = value;
                tmpObj.judgeShame();
            }
        }

        // KILL PLAYER:
        function killPlayer() {
            inGame = false;
            lastDeath = {
                x: player.x,
                y: player.y,
            };

        }

        // UPDATE AGE:
        function updateAge(xp, mxp, age) {
            if (xp != undefined) player.XP = xp;
            if (mxp != undefined) player.maxXP = mxp;
            if (age != undefined) player.age = age;
        }

        // UPDATE UPGRADES:
        function updateUpgrades(points, age) {
            player.upgradePoints = points;
            player.upgrAge = age;
            if (points > 0) {
                tmpList.length = 0;
                utils.removeAllChildren(upgradeHolder);
                for (let i = 0; i < items.weapons.length; ++i) {
                    if (items.weapons[i].age == age && (items.weapons[i].pre == undefined || player.weapons.indexOf(items.weapons[i].pre) >= 0)) {
                        let e = utils.generateElement({
                            id: "upgradeItem" + i,
                            class: "actionBarItem",
                            onmouseout: function () {
                                showItemInfo();
                            },
                            parent: upgradeHolder
                        });
                        e.style.backgroundImage = getEl("actionBarItem" + i).style.backgroundImage;
                        tmpList.push(i);
                    }
                }
                for (let i = 0; i < items.list.length; ++i) {
                    if (items.list[i].age == age && (items.list[i].pre == undefined || player.items.indexOf(items.list[i].pre) >= 0)) {
                        let tmpI = (items.weapons.length + i);
                        let e = utils.generateElement({
                            id: "upgradeItem" + tmpI,
                            class: "actionBarItem",
                            onmouseout: function () {
                                showItemInfo();
                            },
                            parent: upgradeHolder
                        });
                        e.style.backgroundImage = getEl("actionBarItem" + tmpI).style.backgroundImage;
                        tmpList.push(tmpI);
                    }
                }
                for (let i = 0; i < tmpList.length; i++) {
                    (function (i) {
                        let tmpItem = getEl('upgradeItem' + i);
                        tmpItem.onmouseover = function () {
                            if (items.weapons[i]) {
                                showItemInfo(items.weapons[i], true);
                            } else {
                                showItemInfo(items.list[i - items.weapons.length]);
                            }
                        };
                        tmpItem.onclick = utils.checkTrusted(function () {
                            soc.send("H", i);
                        });
                        utils.hookTouchEvents(tmpItem);
                    })(tmpList[i]);
                }
                if (tmpList.length) {
                    upgradeHolder.style.display = "block";
                    upgradeCounter.style.display = "block";
                    upgradeCounter.innerHTML = "SELECT ITEMS (" + points + ")";
                } else {
                    upgradeHolder.style.display = "none";
                    upgradeCounter.style.display = "none";
                    showItemInfo();
                }
            } else {
                upgradeHolder.style.display = "none";
                upgradeCounter.style.display = "none";
                showItemInfo();
            }
        }

        // KILL OBJECT:
        function killObject(sid) {
            let findObj = findObjectBySid(sid);
            objectManager.disableBySid(sid);
        }

        // KILL ALL OBJECTS BY A PLAYER:
        function killObjects(sid) {
            if (player) objectManager.removeAllItems(sid);
        }

        // UPDATE PLAYER DATA:
        function updatePlayers(data) {
            game.tick++;
            enemy = [];
            nears = [];
            near = [];
            game.tickSpeed = performance.now() - game.lastTick;
            game.lastTick = performance.now();
            players.forEach((tmp) => {
                tmp.forcePos = !tmp.visible;
                tmp.visible = false;
            });
            for (let i = 0; i < data.length;) {
                tmpObj = findPlayerBySID(data[i]);
                if (tmpObj) {
                    tmpObj.t1 = (tmpObj.t2 === undefined) ? game.lastTick : tmpObj.t2;
                    tmpObj.t2 = game.lastTick;
                    tmpObj.oldPos.x2 = tmpObj.x2;
                    tmpObj.oldPos.y2 = tmpObj.y2;
                    tmpObj.x1 = tmpObj.x;
                    tmpObj.y1 = tmpObj.y;
                    tmpObj.x2 = data[i + 1];
                    tmpObj.y2 = data[i + 2];
                    tmpObj.x3 = tmpObj.x2 + (tmpObj.x2 - tmpObj.oldPos.x2);
                    tmpObj.y3 = tmpObj.y2 + (tmpObj.y2 - tmpObj.oldPos.y2);
                    tmpObj.d1 = (tmpObj.d2 === undefined) ? data[i + 3] : tmpObj.d2;
                    tmpObj.d2 = data[i + 3];
                    tmpObj.dt = 0;
                    tmpObj.buildIndex = data[i + 4];
                    tmpObj.weaponIndex = data[i + 5];
                    tmpObj.weaponVariant = data[i + 6];
                    tmpObj.team = data[i + 7];
                    tmpObj.isLeader = data[i + 8];
                    tmpObj.oldSkinIndex = tmpObj.skinIndex;
                    tmpObj.oldTailIndex = tmpObj.tailIndex;
                    tmpObj.skinIndex = data[i + 9];
                    tmpObj.tailIndex = data[i + 10];
                    tmpObj.iconIndex = data[i + 11];
                    tmpObj.zIndex = data[i + 12];
                    tmpObj.visible = true;
                    tmpObj.update(game.tickSpeed);
                    tmpObj.dist2 = utils.getDist(tmpObj, player, 2, 2);
                    tmpObj.aim2 = utils.getDirect(tmpObj, player, 2, 2);
                    tmpObj.dist3 = utils.getDist(tmpObj, player, 3, 3);
                    tmpObj.aim3 = utils.getDirect(tmpObj, player, 3, 3);
                    tmpObj.damageThreat = 0;
                    if (tmpObj.skinIndex == 45 && tmpObj.shameTimer <= 0) {
                        tmpObj.addShameTimer();
                    }
                    if (tmpObj.oldSkinIndex == 45 && tmpObj.skinIndex != 45) {
                        tmpObj.shameTimer = 0;
                        tmpObj.shameCount = 0;
                    }
                    if (tmpObj.weaponIndex < 9) {
                        tmpObj.primaryIndex = tmpObj.weaponIndex;
                        tmpObj.primaryVariant = tmpObj.weaponVariant;
                    } else if (tmpObj.weaponIndex > 8) {
                        tmpObj.secondaryIndex = tmpObj.weaponIndex;
                        tmpObj.secondaryVariant = tmpObj.weaponVariant;
                    }
                }
                i += 13;
                if (inGame) {
                    soc.send("D", getAttackDir()); //player dir
                }
            }

            for (let i = 0; i < data.length;) {
                tmpObj = findPlayerBySID(data[i]);
                if (tmpObj) {
                    if (!tmpObj.isTeam(player)) {
                        enemy.push(tmpObj);
                        if (tmpObj.dist2 <= items.weapons[tmpObj.primaryIndex == undefined ? 5 : tmpObj.primaryIndex].range + (player.scale * 2)) {
                            nears.push(tmpObj);
                        }
                    }
                    tmpObj.manageReload();
                }
                i += 13;
            }
            projectiles.forEach((proj) => {
                tmpObj = proj;
                if (tmpObj.active) {
                    tmpObj.tickUpdate(game.tickSpeed);
                }
            });
            if (player && player.alive) {
                if (enemy.length) {
                    near = enemy.sort(function (tmp1, tmp2) {
                        return tmp1.dist2 - tmp2.dist2;
                    })[0];
                }
                if (game.tickQueue[game.tick]) {
                    game.tickQueue[game.tick].forEach((action) => {
                        action();
                    });
                    game.tickQueue[game.tick] = null;
                }

                players.forEach((tmp) => {
                    if (!tmp.visible && player != tmp) {
                        tmp.reloads = {
                            0: 0,
                            1: 0,
                            2: 0,
                            3: 0,
                            4: 0,
                            5: 0,
                            6: 0,
                            7: 0,
                            8: 0,
                            9: 0,
                            10: 0,
                            11: 0,
                            12: 0,
                            13: 0,
                            14: 0,
                            15: 0,
                            53: 0,
                        };
                    }
                    if (tmp.setBullTick) {
                        tmp.bullTimer = 0;
                    }
                    if (tmp.setPoisonTick) {
                        tmp.poisonTimer = 0;
                    }
                    tmp.updateTimer();
                });
                if(inGame) {
                    if (game.tick % 2 == 0) {
                        if (mills.place) {
                            let plcAng = 1.25;
                            for (let i = -plcAng; i <= plcAng; i += plcAng) {
                                checkPlace(3, utils.getDirect(player.oldPos, player, 2, 2) + i);
                            }
                        } else {
                            if (mills.placeSpawnPads) {
                                for (let i = 0; i < Math.PI * 2; i += Math.PI / 2) {
                                    checkPlace(player.getItemType(20), utils.getDirect(player.oldPos, player, 2, 2) + i);
                                }
                            }
                        }
                    }
                }
            }
        }


        // UPDATE LEADERBOARD:
        function updateLeaderboard(data) {
            lastLeaderboardData = data;
        }

        // LOAD GAME OBJECT:
        function loadGameObject(data) {
            for (let i = 0; i < data.length;) {
                objectManager.add(data[i], data[i + 1], data[i + 2], data[i + 3], data[i + 4], data[i + 5], items.list[data[i + 6]], true, (data[i + 7] >= 0 ? {
                    sid: data[i + 7]
                } : null));
                i += 8;
            }
        }

        // ADD AI:
        function loadAI(data) {
            for (let i = 0; i < ais.length; ++i) {
                ais[i].forcePos = !ais[i].visible;
                ais[i].visible = false;
            }
            if (data) {
                let tmpTime = performance.now();
                for (let i = 0; i < data.length;) {
                    tmpObj = findAIBySID(data[i]);
                    if (tmpObj) {
                        tmpObj.index = data[i + 1];
                        tmpObj.t1 = (tmpObj.t2 === undefined) ? tmpTime : tmpObj.t2;
                        tmpObj.t2 = tmpTime;
                        tmpObj.x1 = tmpObj.x;
                        tmpObj.y1 = tmpObj.y;
                        tmpObj.x2 = data[i + 2];
                        tmpObj.y2 = data[i + 3];
                        tmpObj.d1 = (tmpObj.d2 === undefined) ? data[i + 4] : tmpObj.d2;
                        tmpObj.d2 = data[i + 4];
                        tmpObj.health = data[i + 5];
                        tmpObj.dt = 0;
                        tmpObj.visible = true;
                    } else {
                        tmpObj = aiManager.spawn(data[i + 2], data[i + 3], data[i + 4], data[i + 1]);
                        tmpObj.x2 = tmpObj.x;
                        tmpObj.y2 = tmpObj.y;
                        tmpObj.d2 = tmpObj.dir;
                        tmpObj.health = data[i + 5];
                        if (!aiManager.aiTypes[data[i + 1]].name) tmpObj.name = config.cowNames[data[i + 6]];
                        tmpObj.forcePos = true;
                        tmpObj.sid = data[i];
                        tmpObj.visible = true;
                    }
                    i += 7;
                }
            }
        }

        // ANIMATE AI:
        function animateAI(sid) {
            tmpObj = findAIBySID(sid);
            if (tmpObj) tmpObj.startAnim();
        }

        // GATHER ANIMATION:
        function gatherAnimation(sid, didHit, index) {
            tmpObj = findPlayerBySID(sid);
            if (tmpObj) {
                tmpObj.startAnim(didHit, index);
                tmpObj.gatherIndex = index;
                tmpObj.gathering = 1;
                if (didHit) {
                    let tmpObjects = objectManager.hitObj;
                    objectManager.hitObj = [];
                    game.tickBase(() => {
                        // refind
                        tmpObj = findPlayerBySID(sid);
                        let val = items.weapons[index].dmg * (config.weaponVariants[tmpObj[(index < 9 ? "prima" : "seconda") + "ryVariant"]].val) * (items.weapons[index].sDmg || 1) * (tmpObj.skinIndex == 40 ? 3.3 : 1);
                        tmpObjects.forEach((healthy) => {
                            healthy.health -= val;
                        });
                    }, 1);
                }
            }
        }

        // WIGGLE GAME OBJECT:
        function wiggleGameObject(dir, sid) {
            tmpObj = findObjectBySid(sid);
            if (tmpObj) {
                tmpObj.xWiggle += config.gatherWiggle * Math.cos(dir);
                tmpObj.yWiggle += config.gatherWiggle * Math.sin(dir);
                tmpObj.turnPower = 0;
                if (tmpObj.health) {
                    objectManager.hitObj.push(tmpObj);
                }
            }
        }

        // SHOOT TURRET:
        function shootTurret(sid, dir) {
            tmpObj = findObjectBySid(sid);
            if (tmpObj) {

                tmpObj.dir = dir;

                tmpObj.xWiggle += config.gatherWiggle * Math.cos(dir + Math.PI);
                tmpObj.yWiggle += config.gatherWiggle * Math.sin(dir + Math.PI);
            }
        }

        // UPDATE PLAYER VALUE:
        function updatePlayerValue(index, value, updateView) {
            if (player) {
                player[index] = value;
                if (index == "points") {

                } else if (index == "kills") {

                }
            }
        }

        // ACTION BAR:
        function updateItems(data, wpn) {
            if (data) {
                if (wpn) {
                    player.weapons = data;
                    player.primaryIndex = player.weapons[0];
                    player.secondaryIndex = player.weapons[1];
                } else {
                    player.items = data;
                }
            }
            for (let i = 0; i < items.list.length; i++) {
                let tmpI = items.weapons.length + i;
                getEl("actionBarItem" + tmpI).style.display = player.items.indexOf(items.list[i].id) >= 0 ? "inline-block" : "none";
            }
            for (let i = 0; i < items.weapons.length; i++) {
                getEl("actionBarItem" + i).style.display = player.weapons[items.weapons[i].type] == items.weapons[i].id ? "inline-block" : "none";
            }
            let kms = player.weapons[0] == 3 && player.weapons[1] == 15;
            if (kms) {
                getEl("actionBarItem3").style.display = "none";
                getEl("actionBarItem4").style.display = "inline-block";
            }
        }

        // ADD PROJECTILE:
        function addProjectile(x, y, dir, range, speed, indx, layer, sid) {
            projectileManager.addProjectile(x, y, dir, range, speed, indx, null, null, layer, inWindow).sid = sid;
            runAtNextTick.push(Array.prototype.slice.call(arguments));
        }

        // REMOVE PROJECTILE:
        function remProjectile(sid, range) {
            for (let i = 0; i < projectiles.length; ++i) {
                if (projectiles[i].sid == sid) {
                    projectiles[i].range = range;
                    let tmpObjects = objectManager.hitObj;
                    objectManager.hitObj = [];
                    game.tickBase(() => {
                        let val = projectiles[i].dmg;
                        tmpObjects.forEach((healthy) => {
                            if (healthy.projDmg) {
                                healthy.health -= val;
                            }
                        });
                    }, 1);
                }
            }
        }

        // SHOW ALLIANCE MENU:
        function setPlayerTeam(team, isOwner) {
            if (player) {
                player.team = team;
                player.isOwner = isOwner;
                if (team == null) {
                    alliancePlayers = [];
                }
            }
        }

        function setAlliancePlayers(data) {
            alliancePlayers = data;
        }

        // STORE MENU:
        function updateStoreItems(type, id, index) {
            if (index) {
                if (!type) {
                    player.tails[id] = 1;
                }else {
                    player.latestTail = id;
                }
            } else {
                if (!type) {
                    player.skins[id] = 1;
                } else {
                    player.latestSkin = id;
                }
            }
        }

        // SEND MESSAGE:
        function receiveChat(sid, message) {
            let tmpPlayer = findPlayerBySID(sid);
            if (!tmpPlayer.chatMessages) {
                tmpPlayer.chatMessages = [];
            }
            tmpPlayer.chatMessages.push({ message: message, time: Date.now(), alpha: 1 });
            if (tmpPlayer.chatMessages.length > 3) {
                tmpPlayer.chatMessages.shift();
            }
            if (tmpPlayer) {
                tmpPlayer.chatMessage = ((text) => {
                    let tmpString;
                    profanityList.forEach((list) => {
                        if (text.indexOf(list) > -1) {
                            tmpString = "";
                            for (var y = 0; y < list.length; ++y) {
                                tmpString += tmpString.length?"o":"M";
                            }
                            var re = new RegExp(list, 'g');
                            text = text.replace(re, tmpString);
                        }
                    });
                    return text;
                })(message);
                tmpPlayer.chatCountdown = config.chatCountdown;
            }
        }

        // MINIMAP:
        function updateMinimap(data) {
            minimapData = data;
        }

        // SHOW ANIM TEXT:
        function showText(x, y, value, type, color = "#fff") {
            if (typeof value == "number") {
                if (type === -1) {
                    color = "#ee5551";
                    texts.push(new textCore(x, y, value, type, 40, color, texts.length));
                } else {
                    let val = math[1](value);
                    color = value >= 0 ? "#cc5151" : "#8ecc51";
                    texts.push(new textCore(x, y, val, type, math[7](40, val), color, texts.length));
                }
            } else {
                texts.push(new textCore(x, y, value, type, 35, color, texts.length));
            }
        }



        // RENDER LEAF:
        function renderLeaf(x, y, l, r, ctxt) {
            let endX = x + (l * Math.cos(r));
            let endY = y + (l * Math.sin(r));
            let width = l * 0.4;
            ctxt.moveTo(x, y);
            ctxt.beginPath();
            ctxt.quadraticCurveTo(((x + endX) / 2) + (width * Math.cos(r + Math.PI / 2)),
                                  ((y + endY) / 2) + (width * Math.sin(r + Math.PI / 2)), endX, endY);
            ctxt.quadraticCurveTo(((x + endX) / 2) - (width * Math.cos(r + Math.PI / 2)),
                                  ((y + endY) / 2) - (width * Math.sin(r + Math.PI / 2)), x, y);
            ctxt.closePath();
            ctxt.fill();
            ctxt.stroke();
        }

        // RENDER CIRCLE:
        function renderCircle(x, y, scale, tmpContext, dontStroke, dontFill) {
            tmpContext = tmpContext || ctx;
            tmpContext.beginPath();
            tmpContext.arc(x, y, scale, 0, 2 * Math.PI);
            if (!dontFill) tmpContext.fill();
            if (!dontStroke) tmpContext.stroke();
        }

        function renderHealthCircle(x, y, scale, tmpContext, dontStroke, dontFill) {
            tmpContext = tmpContext || ctx;
            tmpContext.beginPath();
            tmpContext.arc(x, y, scale, 0, 2 * Math.PI);
            if (!dontFill) tmpContext.fill();
            if (!dontStroke) tmpContext.stroke();
        }

        // RENDER STAR SHAPE:
        function renderStar(ctxt, spikes, outer, inner) {
            let rot = Math.PI / 2 * 3;
            let x, y;
            let step = Math.PI / spikes;
            ctxt.beginPath();
            ctxt.moveTo(0, -outer);
            for (let i = 0; i < spikes; i++) {
                x = Math.cos(rot) * outer;
                y = Math.sin(rot) * outer;
                ctxt.lineTo(x, y);
                rot += step;
                x = Math.cos(rot) * inner;
                y = Math.sin(rot) * inner;
                ctxt.lineTo(x, y);
                rot += step;
            }
            ctxt.lineTo(0, -outer);
            ctxt.closePath();
        }

        function renderHealthStar(ctxt, spikes, outer, inner) {
            let rot = Math.PI / 2 * 3;
            let x, y;
            let step = Math.PI / spikes;
            ctxt.beginPath();
            ctxt.moveTo(0, -outer);
            for (let i = 0; i < spikes; i++) {
                x = Math.cos(rot) * outer;
                y = Math.sin(rot) * outer;
                ctxt.lineTo(x, y);
                rot += step;
                x = Math.cos(rot) * inner;
                y = Math.sin(rot) * inner;
                ctxt.lineTo(x, y);
                rot += step;
            }
            ctxt.lineTo(0, -outer);
            ctxt.closePath();
        }

        // RENDER RECTANGLE:
        function renderRect(x, y, w, h, ctxt, dontStroke, dontFill) {
            if (!dontFill) ctxt.fillRect(x - (w / 2), y - (h / 2), w, h);
            if (!dontStroke) ctxt.strokeRect(x - (w / 2), y - (h / 2), w, h);
        }

        function renderHealthRect(x, y, w, h, ctxt, dontStroke, dontFill) {
            if (!dontFill) ctxt.fillRect(x - (w / 2), y - (h / 2), w, h);
            if (!dontStroke) ctxt.strokeRect(x - (w / 2), y - (h / 2), w, h);
        }

        // RENDER RECTCIRCLE:
        function renderRectCircle(x, y, s, sw, seg, ctxt, dontStroke, dontFill) {
            ctxt.save();
            ctxt.translate(x, y);
            seg = Math.ceil(seg / 2);
            for (let i = 0; i < seg; i++) {
                renderRect(0, 0, s * 2, sw, ctxt, dontStroke, dontFill);
                ctxt.rotate(Math.PI / seg);
            }
            ctxt.restore();
        }

        // RENDER BLOB:
        function renderBlob(ctxt, spikes, outer, inner) {
            let rot = Math.PI / 2 * 3;
            let x, y;
            let step = Math.PI / spikes;
            let tmpOuter;
            ctxt.beginPath();
            ctxt.moveTo(0, -inner);
            for (let i = 0; i < spikes; i++) {
                tmpOuter = utils.randInt(outer + 0.9, outer * 1.2);
                ctxt.quadraticCurveTo(Math.cos(rot + step) * tmpOuter, Math.sin(rot + step) * tmpOuter,
                                      Math.cos(rot + (step * 2)) * inner, Math.sin(rot + (step * 2)) * inner);
                rot += step * 2;
            }
            ctxt.lineTo(0, -inner);
            ctxt.closePath();
        }

        // RENDER TRIANGLE:
        function renderTriangle(s, ctx) {
            ctx = ctx || ctx;
            let h = s * (Math.sqrt(3) / 2);
            ctx.beginPath();
            ctx.moveTo(0, -h / 2);
            ctx.lineTo(-s / 2, h / 2);
            ctx.lineTo(s / 2, h / 2);
            ctx.lineTo(0, -h / 2);
            ctx.fill();
            ctx.closePath();
        }

        // RENDER POLYGON:
        function renderPolygon(ctx, sides, diameter) {
            let lineWidth = ctx.lineWidth || 0;
            let radius = diameter / 2;
            ctx.beginPath();
            let angles = (Math.PI * 2) / sides;

            for (let index = 0; index < sides; index++) {
                let x = radius + (radius - lineWidth / 2) * Math.cos(angles * index);
                let y = radius + (radius - lineWidth / 2) * Math.sin(angles * index);
                ctx.lineTo(x, y);
            }

            ctx.closePath();
        }


        // PREPARE MENU BACKGROUND:
        function prepareMenuBackground() {
            let tmpMid = config.mapScale / 2;
            let attempts = 0;
            for (let i = 0; i < items.list.length*3;) {
                if (attempts >= 1000) break;
                attempts++;
                let type = items.list[utils.randInt(0, items.list.length - 1)];
                let data = {
                    x: tmpMid + utils.randFloat(-1000, 1000),
                    y: tmpMid + utils.randFloat(-600, 600),
                    dir: utils.fixTo(Math.random() * (Math.PI * 2), 2)
                };
                if (objectManager.checkItemLocation(data.x, data.y, type.scale, 0.6, type.id, true)) {
                    objectManager.add(i, data.x, data.y, data.dir, type.scale, type.id, type);
                } else {
                    continue;
                }
                i++;
            }
        }

        // RENDER PLAYERS:
        function renderPlayers(xOffset, yOffset, zIndex) {
            ctx.globalAlpha = 1;
            ctx.fillStyle = "#91b2db";
            for (var i = 0; i < players.length; ++i) {
                tmpObj = players[i];
                if (tmpObj.zIndex == zIndex) {
                    tmpObj.animate(delta);
                    if (tmpObj.visible) {
                        tmpObj.skinRot += (0.002 * delta);
                        tmpDir = (tmpObj == player) ? getAttackDir() : tmpObj.dir;
                        ctx.save();
                        ctx.translate(tmpObj.x - xOffset, tmpObj.y - yOffset);

                        // RENDER PLAYER:
                        ctx.rotate(tmpDir + tmpObj.dirPlus);
                        renderPlayer(tmpObj, ctx);
                        ctx.restore();

                    }
                }
            }
        }

        // RENDER PLAYER:
        function renderPlayer(obj, ctxt) {
            ctxt = ctxt || ctx;
            ctxt.lineWidth = outlineWidth;
            ctxt.lineJoin = "miter";
            let handAngle = (Math.PI / 4) * (items.weapons[obj.weaponIndex].armS||1);
            let oHandAngle = (obj.buildIndex < 0)?(items.weapons[obj.weaponIndex].hndS||1):1;
            let oHandDist = (obj.buildIndex < 0)?(items.weapons[obj.weaponIndex].hndD||1):1;

            let katanaMusket = (obj == player && obj.weapons[0] == 3 && obj.weapons[1] == 15);

            // TAIL/CAPE:
            if (obj.tailIndex > 0) {
                renderTail(obj.tailIndex, ctxt, obj);
            }

            // WEAPON BELLOW HANDS:
            if (obj.buildIndex < 0 && !items.weapons[obj.weaponIndex].aboveHand) {
                renderTool(items.weapons[katanaMusket ? 4 : obj.weaponIndex], config.weaponVariants[obj.weaponVariant].src, obj.scale, 0, ctxt);
                if (items.weapons[obj.weaponIndex].projectile != undefined && !items.weapons[obj.weaponIndex].hideProjectile) {
                    renderProjectile(obj.scale, 0,
                                     items.projectiles[items.weapons[obj.weaponIndex].projectile], ctx);
                }
            }

            // HANDS:
            ctxt.fillStyle = config.skinColors[obj.skinColor];
            renderCircle(obj.scale * Math.cos(handAngle), (obj.scale * Math.sin(handAngle)), 14);
            renderCircle((obj.scale * oHandDist) * Math.cos(-handAngle * oHandAngle),
                         (obj.scale * oHandDist) * Math.sin(-handAngle * oHandAngle), 14);

            // WEAPON ABOVE HANDS:
            if (obj.buildIndex < 0 && items.weapons[obj.weaponIndex].aboveHand) {
                renderTool(items.weapons[obj.weaponIndex], config.weaponVariants[obj.weaponVariant].src, obj.scale, 0, ctxt);
                if (items.weapons[obj.weaponIndex].projectile != undefined && !items.weapons[obj.weaponIndex].hideProjectile) {
                    renderProjectile(obj.scale, 0,
                                     items.projectiles[items.weapons[obj.weaponIndex].projectile], ctx);
                }
            }

            // BUILD ITEM:
            if (obj.buildIndex >= 0) {
                var tmpSprite = getItemSprite(items.list[obj.buildIndex]);
                ctxt.drawImage(tmpSprite, obj.scale - items.list[obj.buildIndex].holdOffset, -tmpSprite.width / 2);
            }

            // BODY:
            renderCircle(0, 0, obj.scale, ctxt);

            // SKIN:
            if (obj.skinIndex > 0) {
                ctxt.rotate(Math.PI/2);
                renderSkin(obj.skinIndex, ctxt, null, obj);
            }

        }

        // RENDER SKINS:
        let skinSprites = {};
        let skinPointers = {};
        let tmpSkin;
        function renderSkin(index, ctxt, parentSkin, owner) {
            tmpSkin = skinSprites[index];
            if (!tmpSkin) {
                let tmpImage = new Image();
                tmpImage.onload = function() {
                    this.isLoaded = true;
                    this.onload = null;
                };
                tmpImage.src = "https://moomoo.io/img/hats/hat_" + index + ".png";
                skinSprites[index] = tmpImage;
                tmpSkin = tmpImage;
            }
            let tmpObj = parentSkin||skinPointers[index];
            if (!tmpObj) {
                for (let i = 0; i < hats.length; ++i) {
                    if (hats[i].id == index) {
                        tmpObj = hats[i];
                        break;
                    }
                }
                skinPointers[index] = tmpObj;
            }
            if (tmpSkin.isLoaded) ctxt.drawImage(tmpSkin, -tmpObj.scale/2, -tmpObj.scale/2, tmpObj.scale, tmpObj.scale);
            if (!parentSkin && tmpObj.topSprite) {
                ctxt.save();
                ctxt.rotate(owner.skinRot);
                renderSkin(index + "_top", ctxt, tmpObj, owner);
                ctxt.restore();
            }
        }

        // RENDER TAIL:
        let accessSprites = {};
        let accessPointers = {};
        function renderTail(index, ctxt, owner) {
            tmpSkin = accessSprites[index];
            if (!tmpSkin) {
                let tmpImage = new Image();
                tmpImage.onload = function() {
                    this.isLoaded = true;
                    this.onload = null;
                };
                tmpImage.src = "https://moomoo.io/img/accessories/access_" + index + ".png";
                accessSprites[index] = tmpImage;
                tmpSkin = tmpImage;
            }
            let tmpObj = accessPointers[index];
            if (!tmpObj) {
                for (let i = 0; i < accessories.length; ++i) {
                    if (accessories[i].id == index) {
                        tmpObj = accessories[i];
                        break;
                    }
                }
                accessPointers[index] = tmpObj;
            }
            if (tmpSkin.isLoaded) {
                ctxt.save();
                ctxt.translate(-20 - (tmpObj.xOff || 0), 0);
                if (tmpObj.spin) ctxt.rotate(owner.skinRot);
                ctxt.drawImage(tmpSkin, -(tmpObj.scale / 2), -(tmpObj.scale / 2), tmpObj.scale, tmpObj.scale);
                ctxt.restore();
            }
        }

        // RENDER TOOL:
        let toolSprites = {};
        function renderTool(obj, variant, x, y, ctxt) {
            let tmpSrc = obj.src + (variant||"");
            let tmpSprite = toolSprites[tmpSrc];
            if (!tmpSprite) {
                tmpSprite = new Image();
                tmpSprite.onload = function() {
                    this.isLoaded = true;
                }
                tmpSprite.src = "https://moomoo.io/img/weapons/" + tmpSrc + ".png";
                toolSprites[tmpSrc] = tmpSprite;
            }
            if (tmpSprite.isLoaded) ctxt.drawImage(tmpSprite, x + obj.xOff - (obj.length / 2), y + obj.yOff - (obj.width / 2), obj.length, obj.width);
        }

        // RENDER PROJECTILES:
        function renderProjectiles(layer, xOffset, yOffset) {
            for(let i = 0; i < projectiles.length; i++) {
                tmpObj = projectiles[i];
                if (tmpObj.active && tmpObj.layer == layer && tmpObj.inWindow) {
                    tmpObj.update(delta);
                    if (tmpObj.active && isOnScreen(tmpObj.x - xOffset, tmpObj.y - yOffset, tmpObj.scale)) {
                        ctx.save();
                        ctx.translate(tmpObj.x - xOffset, tmpObj.y - yOffset);
                        ctx.rotate(tmpObj.dir);
                        renderProjectile(0, 0, tmpObj, ctx, 1);
                        ctx.restore();
                    }
                }
            };
        }

        // RENDER PROJECTILE:
        let projectileSprites = {};
        function renderProjectile(x, y, obj, ctxt, debug) {
            if (obj.src) {
                let tmpSrc = items.projectiles[obj.indx].src;
                let tmpSprite = projectileSprites[tmpSrc];
                if (!tmpSprite) {
                    tmpSprite = new Image();
                    tmpSprite.onload = function() {
                        this.isLoaded = true;
                    }
                    tmpSprite.src = "https://moomoo.io/img/weapons/" + tmpSrc + ".png";
                    projectileSprites[tmpSrc] = tmpSprite;
                }
                if (tmpSprite.isLoaded) ctxt.drawImage(tmpSprite, x - (obj.scale / 2), y - (obj.scale / 2), obj.scale, obj.scale);
            } else if (obj.indx == 1) {
                ctxt.fillStyle = "#939393";
                renderCircle(x, y, obj.scale, ctxt);
            }
        }

        // RENDER AI:
        let aiSprites = {};
        function renderAI(obj, ctxt) {
            let tmpIndx = obj.index;
            let tmpSprite = aiSprites[tmpIndx];
            if (!tmpSprite) {
                let tmpImg = new Image();
                tmpImg.onload = function() {
                    this.isLoaded = true;
                    this.onload = null;
                };
                tmpImg.src = "https://moomoo.io/img/animals/" + obj.src + ".png";
                console.log(tmpImg.src);
                tmpSprite = tmpImg;
                aiSprites[tmpIndx] = tmpSprite;
            }
            if (tmpSprite.isLoaded) {
                let tmpScale = obj.scale * 1.2 * (obj.spriteMlt || 1);
                ctxt.drawImage(tmpSprite, -tmpScale, -tmpScale, tmpScale * 2, tmpScale * 2);
            }
        }

        // RENDER WATER BODIES:
        function renderWaterBodies(xOffset, yOffset, ctxt, padding) {

            // MIDDLE RIVER:
            let tmpW = config.riverWidth + padding;
            let tmpY = (config.mapScale / 2) - yOffset - (tmpW / 2);
            if (tmpY < screen.max.height && tmpY + tmpW > 0) {
                ctxt.fillRect(0, tmpY, screen.max.width, tmpW);
            }
        }

        let gameObjectSprites = {};
        function getResSprite(obj) {
            let biomeID = (obj.y>=config.mapScale-config.snowBiomeTop)?2:((obj.y<=config.snowBiomeTop)?1:0);
            let tmpIndex = (obj.type + "_" + obj.scale + "_" + biomeID);
            let tmpSprite = gameObjectSprites[tmpIndex];
            if (!tmpSprite) {
                let blurScale = 15;
                let tmpCanvas = document.createElement("canvas");
                tmpCanvas.width = tmpCanvas.height = (obj.scale * 2.1) + outlineWidth;
                let tmpContext = tmpCanvas.getContext('2d');
                tmpContext.translate((tmpCanvas.width / 2), (tmpCanvas.height / 2));
                tmpContext.rotate(utils.randFloat(0, Math.PI));
                tmpContext.strokeStyle = outlineColor;
                tmpContext.lineWidth = outlineWidth;
                if (obj.type == 0) {
                    let tmpScale;
                    let tmpCount = utils.randInt(5, 7);
                    tmpContext.globalAlpha = 0.8;
                    for (let i = 0; i < 2; ++i) {
                        tmpScale = tmpObj.scale * (!i?1:0.5);
                        renderStar(tmpContext, tmpCount, tmpScale, tmpScale * 0.7);
                        tmpContext.fillStyle = !biomeID?(!i?"#9ebf57":"#b4db62"):(!i?"#e3f1f4":"#fff");
                        tmpContext.fill();
                        if (!i) {
                            tmpContext.stroke();
                            tmpContext.shadowBlur = null;
                            tmpContext.shadowColor = null;
                            tmpContext.globalAlpha = 1;
                        }
                    }
                } else if (obj.type == 1) {
                    if (biomeID == 2) {
                        tmpContext.fillStyle = "#606060";
                        renderStar(tmpContext, 6, obj.scale * 0.3, obj.scale * 0.71);
                        tmpContext.fill();
                        tmpContext.stroke();

                        //tmpContext.shadowBlur = null;
                        //tmpContext.shadowColor = null;

                        tmpContext.fillStyle = "#89a54c";
                        renderCircle(0, 0, obj.scale * 0.55, tmpContext);
                        tmpContext.fillStyle = "#a5c65b";
                        renderCircle(0, 0, obj.scale * 0.3, tmpContext, true);
                    } else {
                        renderBlob(tmpContext, 6, tmpObj.scale, tmpObj.scale * 0.7);
                        tmpContext.fillStyle = biomeID?"#e3f1f4":"#89a54c";
                        tmpContext.fill();
                        tmpContext.stroke();

                        //tmpContext.shadowBlur = null;
                        //tmpContext.shadowColor = null;

                        tmpContext.fillStyle = biomeID?"#6a64af":"#c15555";
                        let tmpRange;
                        let berries = 4;
                        let rotVal = (Math.PI * 2) / berries;
                        for (let i = 0; i < berries; ++i) {
                            tmpRange = utils.randInt(tmpObj.scale/3.5, tmpObj.scale/2.3);
                            renderCircle(tmpRange * Math.cos(rotVal * i), tmpRange * Math.sin(rotVal * i),
                                         utils.randInt(10, 12), tmpContext);
                        }
                    }
                } else if (obj.type == 2 || obj.type == 3) {
                    tmpContext.fillStyle = (obj.type==2)?(biomeID==2?"#938d77":"#939393"):"#e0c655";
                    renderStar(tmpContext, 3, obj.scale, obj.scale);
                    tmpContext.fill();
                    tmpContext.stroke();

                    tmpContext.shadowBlur = null;
                    tmpContext.shadowColor = null;

                    tmpContext.fillStyle = (obj.type==2)?(biomeID==2?"#b2ab90":"#bcbcbc"):"#ebdca3";
                    renderStar(tmpContext, 3, obj.scale * 0.55, obj.scale * 0.65);
                    tmpContext.fill();
                }
                tmpSprite = tmpCanvas;
                gameObjectSprites[tmpIndex] = tmpSprite;
            }
            return tmpSprite;
        }

        // GET ITEM SPRITE:
        let itemSprites = [];
        function getItemSprite(obj, asIcon) {
            let tmpSprite = itemSprites[obj.id];
            if (!tmpSprite || asIcon) {
                let blurScale = 0;
                let tmpCanvas = document.createElement("canvas");
                let reScale = ((!asIcon && obj.name == "windmill") ? items.list[4].scale : obj.scale);
                tmpCanvas.width = tmpCanvas.height = (reScale * 2.5) + outlineWidth + (items.list[obj.id].spritePadding || 0) + blurScale;
                let tmpContext = tmpCanvas.getContext("2d");
                tmpContext.translate((tmpCanvas.width / 2), (tmpCanvas.height / 2));
                tmpContext.rotate(asIcon ? 0 : (Math.PI / 2));
                tmpContext.strokeStyle = outlineColor;
                tmpContext.lineWidth = outlineWidth * (asIcon ? (tmpCanvas.width / 81) : 1);

                if (obj.name == "apple") {
                    tmpContext.fillStyle = "#c15555";
                    renderCircle(0, 0, obj.scale, tmpContext);
                    tmpContext.fillStyle = "#89a54c";
                    let leafDir = -(Math.PI / 2);
                    renderLeaf(obj.scale * Math.cos(leafDir), obj.scale * Math.sin(leafDir),
                               25, leafDir + Math.PI / 2, tmpContext);
                } else if (obj.name == "cookie") {
                    tmpContext.fillStyle = "#cca861";
                    renderCircle(0, 0, obj.scale, tmpContext);
                    tmpContext.fillStyle = "#937c4b";
                    let chips = 4;
                    let rotVal = (Math.PI * 2) / chips;
                    let tmpRange;
                    for (let i = 0; i < chips; ++i) {
                        tmpRange = utils.randInt(obj.scale / 2.5, obj.scale / 1.7);
                        renderCircle(tmpRange * Math.cos(rotVal * i), tmpRange * Math.sin(rotVal * i),
                                     utils.randInt(4, 5), tmpContext, true);
                    }
                } else if (obj.name == "cheese") {
                    tmpContext.fillStyle = "#f4f3ac";
                    renderCircle(0, 0, obj.scale, tmpContext);
                    tmpContext.fillStyle = "#c3c28b";
                    let chips = 4;
                    let rotVal = (Math.PI * 2) / chips;
                    let tmpRange;
                    for (let i = 0; i < chips; ++i) {
                        tmpRange = utils.randInt(obj.scale / 2.5, obj.scale / 1.7);
                        renderCircle(tmpRange * Math.cos(rotVal * i), tmpRange * Math.sin(rotVal * i),
                                     utils.randInt(4, 5), tmpContext, true);
                    }
                } else if (obj.name == "wood wall" || obj.name == "stone wall" || obj.name == "castle wall") {
                    tmpContext.fillStyle = (obj.name == "castle wall") ? "#83898e" : (obj.name == "wood wall") ?
                        "#a5974c" : "#939393";
                    let sides = (obj.name == "castle wall") ? 4 : 3;
                    renderStar(tmpContext, sides, obj.scale * 1.1, obj.scale * 1.1);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.fillStyle = (obj.name == "castle wall") ? "#9da4aa" : (obj.name == "wood wall") ?
                        "#c9b758" : "#bcbcbc";
                    renderStar(tmpContext, sides, obj.scale * 0.65, obj.scale * 0.65);
                    tmpContext.fill();
                } else if (obj.name == "spikes" || obj.name == "greater spikes" || obj.name == "poison spikes" ||
                           obj.name == "spinning spikes") {
                    tmpContext.fillStyle = (obj.name == "poison spikes") ? "#7b935d" : "#939393";
                    let tmpScale = (obj.scale * 0.6);
                    renderStar(tmpContext, (obj.name == "spikes") ? 5 : 6, obj.scale, tmpScale);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.fillStyle = "#a5974c";
                    renderCircle(0, 0, tmpScale, tmpContext);
                    tmpContext.fillStyle = "#c9b758";
                    renderCircle(0, 0, tmpScale / 2, tmpContext, true);
                } else if (obj.name == "windmill" || obj.name == "faster windmill" || obj.name == "power mill") {
                    tmpContext.fillStyle = "#a5974c";
                    renderCircle(0, 0, reScale, tmpContext);
                    tmpContext.fillStyle = "#c9b758";
                    renderRectCircle(0, 0, reScale * 1.5, 29, 4, tmpContext);
                    tmpContext.fillStyle = "#a5974c";
                    renderCircle(0, 0, reScale * 0.5, tmpContext);
                } else if (obj.name == "mine") {
                    tmpContext.fillStyle = "#939393";
                    renderStar(tmpContext, 3, obj.scale, obj.scale);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.fillStyle = "#bcbcbc";
                    renderStar(tmpContext, 3, obj.scale * 0.55, obj.scale * 0.65);
                    tmpContext.fill();
                } else if (obj.name == "sapling") {
                    for (let i = 0; i < 2; ++i) {
                        let tmpScale = obj.scale * (!i ? 1 : 0.5);
                        renderStar(tmpContext, 7, tmpScale, tmpScale * 0.7);
                        tmpContext.fillStyle = (!i ? "#9ebf57" : "#b4db62");
                        tmpContext.fill();
                        if (!i) tmpContext.stroke();
                    }
                } else if (obj.name == "pit trap") {
                    tmpContext.fillStyle = "#a5974c";
                    renderStar(tmpContext, 3, obj.scale * 1.1, obj.scale * 1.1);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.fillStyle = outlineColor;
                    renderStar(tmpContext, 3, obj.scale * 0.65, obj.scale * 0.65);
                    tmpContext.fill();
                } else if (obj.name == "boost pad") {
                    tmpContext.fillStyle = "#7e7f82";
                    renderRect(0, 0, obj.scale * 2, obj.scale * 2, tmpContext);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.fillStyle = "#dbd97d";
                    renderTriangle(obj.scale * 1, tmpContext);
                } else if (obj.name == "turret") {
                    tmpContext.fillStyle = "#a5974c";
                    renderCircle(0, 0, obj.scale, tmpContext);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.fillStyle = "#939393";
                    let tmpLen = 50;
                    renderRect(0, -tmpLen / 2, obj.scale * 0.9, tmpLen, tmpContext);
                    renderCircle(0, 0, obj.scale * 0.6, tmpContext);
                    tmpContext.fill();
                    tmpContext.stroke();
                } else if (obj.name == "platform") {
                    tmpContext.fillStyle = "#cebd5f";
                    let tmpCount = 4;
                    let tmpS = obj.scale * 2;
                    let tmpW = tmpS / tmpCount;
                    let tmpX = -(obj.scale / 2);
                    for (let i = 0; i < tmpCount; ++i) {
                        renderRect(tmpX - (tmpW / 2), 0, tmpW, obj.scale * 2, tmpContext);
                        tmpContext.fill();
                        tmpContext.stroke();
                        tmpX += tmpS / tmpCount;
                    }
                } else if (obj.name == "healing pad") {
                    tmpContext.fillStyle = "#7e7f82";
                    renderRect(0, 0, obj.scale * 2, obj.scale * 2, tmpContext);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.fillStyle = "#db6e6e";
                    renderRectCircle(0, 0, obj.scale * 0.65, 20, 4, tmpContext, true);
                } else if (obj.name == "spawn pad") {
                    tmpContext.fillStyle = "#7e7f82";
                    renderRect(0, 0, obj.scale * 2, obj.scale * 2, tmpContext);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.fillStyle = "#71aad6";
                    renderCircle(0, 0, obj.scale * 0.6, tmpContext);
                } else if (obj.name == "blocker") {
                    tmpContext.fillStyle = "#7e7f82";
                    renderCircle(0, 0, obj.scale, tmpContext);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.rotate(Math.PI / 4);
                    tmpContext.fillStyle = "#db6e6e";
                    renderRectCircle(0, 0, obj.scale * 0.65, 20, 4, tmpContext, true);
                } else if (obj.name == "teleporter") {
                    tmpContext.fillStyle = "#7e7f82";
                    renderCircle(0, 0, obj.scale, tmpContext);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpContext.rotate(Math.PI / 4);
                    tmpContext.fillStyle = "#d76edb";
                    renderCircle(0, 0, obj.scale * 0.5, tmpContext, true);
                }

                tmpSprite = tmpCanvas;
                if (!asIcon) itemSprites[obj.id] = tmpSprite;
            }
            return tmpSprite;
        }

        function getItemSprite2(obj, tmpX, tmpY) {
            let tmpContext = ctx;
            let reScale = (obj.name == "windmill" ? items.list[4].scale : obj.scale);
            tmpContext.save();
            tmpContext.translate(tmpX, tmpY);
            tmpContext.rotate(obj.dir);
            tmpContext.strokeStyle = outlineColor;
            tmpContext.lineWidth = outlineWidth;
            if (obj.name == "apple") {
                tmpContext.fillStyle = "#c15555";
                renderCircle(0, 0, obj.scale, tmpContext);
                tmpContext.fillStyle = "#89a54c";
                let leafDir = -(Math.PI / 2);
                renderLeaf(obj.scale * Math.cos(leafDir), obj.scale * Math.sin(leafDir),
                           25, leafDir + Math.PI / 2, tmpContext);
            } else if (obj.name == "cookie") {
                tmpContext.fillStyle = "#cca861";
                renderCircle(0, 0, obj.scale, tmpContext);
                tmpContext.fillStyle = "#937c4b";
                let chips = 4;
                let rotVal = (Math.PI * 2) / chips;
                let tmpRange;
                for (let i = 0; i < chips; ++i) {
                    tmpRange = utils.randInt(obj.scale / 2.5, obj.scale / 1.7);
                    renderCircle(tmpRange * Math.cos(rotVal * i), tmpRange * Math.sin(rotVal * i),
                                 utils.randInt(4, 5), tmpContext, true);
                }
            } else if (obj.name == "cheese") {
                tmpContext.fillStyle = "#f4f3ac";
                renderCircle(0, 0, obj.scale, tmpContext);
                tmpContext.fillStyle = "#c3c28b";
                let chips = 4;
                let rotVal = (Math.PI * 2) / chips;
                let tmpRange;
                for (let i = 0; i < chips; ++i) {
                    tmpRange = utils.randInt(obj.scale / 2.5, obj.scale / 1.7);
                    renderCircle(tmpRange * Math.cos(rotVal * i), tmpRange * Math.sin(rotVal * i),
                                 utils.randInt(4, 5), tmpContext, true);
                }
            } else if (obj.name == "wood wall" || obj.name == "stone wall" || obj.name == "castle wall") {
                tmpContext.fillStyle = (obj.name == "castle wall") ? "#83898e" : (obj.name == "wood wall") ?
                    "#a5974c" : "#939393";
                let sides = (obj.name == "castle wall") ? 4 : 3;
                renderStar(tmpContext, sides, obj.scale * 1.1, obj.scale * 1.1);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.fillStyle = (obj.name == "castle wall") ? "#9da4aa" : (obj.name == "wood wall") ?
                    "#c9b758" : "#bcbcbc";
                renderStar(tmpContext, sides, obj.scale * 0.65, obj.scale * 0.65);
                tmpContext.fill();
            } else if (obj.name == "spikes" || obj.name == "greater spikes" || obj.name == "poison spikes" ||
                       obj.name == "spinning spikes") {
                tmpContext.fillStyle = (obj.name == "poison spikes") ? "#7b935d" : "#939393";
                let tmpScale = (obj.scale * 0.6);
                renderStar(tmpContext, (obj.name == "spikes") ? 5 : 6, obj.scale, tmpScale);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.fillStyle = "#a5974c";
                renderCircle(0, 0, tmpScale, tmpContext);
                tmpContext.fillStyle = "#c9b758";
                renderCircle(0, 0, tmpScale / 2, tmpContext, true);
            } else if (obj.name == "windmill" || obj.name == "faster windmill" || obj.name == "power mill") {
                tmpContext.fillStyle = "#a5974c";
                renderCircle(0, 0, reScale, tmpContext);
                tmpContext.fillStyle = "#c9b758";
                renderRectCircle(0, 0, reScale * 1.5, 29, 4, tmpContext);
                tmpContext.fillStyle = "#a5974c";
                renderCircle(0, 0, reScale * 0.5, tmpContext);
            } else if (obj.name == "mine") {
                tmpContext.fillStyle = "#939393";
                renderStar(tmpContext, 3, obj.scale, obj.scale);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.fillStyle = "#bcbcbc";
                renderStar(tmpContext, 3, obj.scale * 0.55, obj.scale * 0.65);
                tmpContext.fill();
            } else if (obj.name == "sapling") {
                for (let i = 0; i < 2; ++i) {
                    let tmpScale = obj.scale * (!i ? 1 : 0.5);
                    renderStar(tmpContext, 7, tmpScale, tmpScale * 0.7);
                    tmpContext.fillStyle = (!i ? "#9ebf57" : "#b4db62");
                    tmpContext.fill();
                    if (!i) tmpContext.stroke();
                }
            } else if (obj.name == "pit trap") {
                tmpContext.fillStyle = "#a5974c";
                renderStar(tmpContext, 3, obj.scale * 1.1, obj.scale * 1.1);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.fillStyle = outlineColor;
                renderStar(tmpContext, 3, obj.scale * 0.65, obj.scale * 0.65);
                tmpContext.fill();
            } else if (obj.name == "boost pad") {
                tmpContext.fillStyle = "#7e7f82";
                renderRect(0, 0, obj.scale * 2, obj.scale * 2, tmpContext);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.fillStyle = "#dbd97d";
                renderTriangle(obj.scale * 1, tmpContext);
            } else if (obj.name == "turret") {
                tmpContext.fillStyle = "#a5974c";
                renderCircle(0, 0, obj.scale, tmpContext);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.fillStyle = "#939393";
                let tmpLen = 50;
                renderRect(0, -tmpLen / 2, obj.scale * 0.9, tmpLen, tmpContext);
                renderCircle(0, 0, obj.scale * 0.6, tmpContext);
                tmpContext.fill();
                tmpContext.stroke();
            } else if (obj.name == "platform") {
                tmpContext.fillStyle = "#cebd5f";
                let tmpCount = 4;
                let tmpS = obj.scale * 2;
                let tmpW = tmpS / tmpCount;
                let tmpX = -(obj.scale / 2);
                for (let i = 0; i < tmpCount; ++i) {
                    renderRect(tmpX - (tmpW / 2), 0, tmpW, obj.scale * 2, tmpContext);
                    tmpContext.fill();
                    tmpContext.stroke();
                    tmpX += tmpS / tmpCount;
                }
            } else if (obj.name == "healing pad") {
                tmpContext.fillStyle = "#7e7f82";
                renderRect(0, 0, obj.scale * 2, obj.scale * 2, tmpContext);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.fillStyle = "#db6e6e";
                renderRectCircle(0, 0, obj.scale * 0.65, 20, 4, tmpContext, true);
            } else if (obj.name == "spawn pad") {
                tmpContext.fillStyle = "#7e7f82";
                renderRect(0, 0, obj.scale * 2, obj.scale * 2, tmpContext);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.fillStyle = "#71aad6";
                renderCircle(0, 0, obj.scale * 0.6, tmpContext);
            } else if (obj.name == "blocker") {
                tmpContext.fillStyle = "#7e7f82";
                renderCircle(0, 0, obj.scale, tmpContext);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.rotate(Math.PI / 4);
                tmpContext.fillStyle = "#db6e6e";
                renderRectCircle(0, 0, obj.scale * 0.65, 20, 4, tmpContext, true);
            } else if (obj.name == "teleporter") {
                tmpContext.fillStyle = "#7e7f82";
                renderCircle(0, 0, obj.scale, tmpContext);
                tmpContext.fill();
                tmpContext.stroke();
                tmpContext.rotate(Math.PI / 4);
                tmpContext.fillStyle = "#d76edb";
                renderCircle(0, 0, obj.scale * 0.5, tmpContext, true);
            }
            tmpContext.restore();
        }

        // RENDER SRC OBjCET:
        function renderResSprite(obj, tmpX, tmpY) {
            let sprite = getResSprite(obj);
            ctx.globalAlpha = obj.alpha;
            ctx.save();
            ctx.translate(tmpX, tmpY);
            ctx.rotate(obj.dir);
            ctx.scale(obj.scaleFace, obj.scaleFace);
            ctx.drawImage(sprite, -sprite.width / 2, -sprite.height / 2);
            ctx.restore();
        }

        // OBJECT ON SCREEN:
        function isOnScreen(x, y, s) {
            return (x + s >= 0 && x - s <= screen.max.width && y + s >= 0 && (y, s, screen.max.height));
        }

        function createVolcano() {
            let volcanoDimension = config.volcanoScale * 2;
            let landCanvas = document.createElement("canvas");
            landCanvas.width = volcanoDimension;
            landCanvas.height = volcanoDimension;
            let landContext = landCanvas.getContext("2d");
            landContext.strokeStyle = "#3e3e3e";
            landContext.lineWidth = 5.5 * 2;
            landContext.fillStyle = "#7f7f7f";
            renderPolygon(landContext, 10, volcanoDimension);
            landContext.fill();
            landContext.stroke();
            volcano.land = landCanvas;

            let lavaCanvas = document.createElement("canvas");
            let lavaDimension = config.innerVolcanoScale * 2;
            lavaCanvas.width = lavaDimension;
            lavaCanvas.height = lavaDimension;

            let lavaContext = lavaCanvas.getContext("2d");
            lavaContext.strokeStyle = "#525252";
            lavaContext.lineWidth = 5.5 * 1.6;
            lavaContext.fillStyle = "#f54e16";
            lavaContext.strokeStyle = "#f56f16";

            renderPolygon(lavaContext, 10, lavaDimension);
            lavaContext.fill();
            lavaContext.stroke();
            volcano.lava = lavaCanvas;
        }
        createVolcano();
        function renderVolcano() {
            let XOffset = xOffset;
            let YOffset = yOffset;
            volcano.animationTime += delta;
            volcano.animationTime %= config.volcanoAnimationDuration;
            let halfAnimationDuration = config.volcanoAnimationDuration / 2;
            let lavaScaleFactor = 1.7 + .3 * (Math.abs(halfAnimationDuration - volcano.animationTime) / halfAnimationDuration);
            let finalLavaScale = config.innerVolcanoScale * lavaScaleFactor;
            ctx.drawImage(volcano.land, volcano.x - config.volcanoScale - XOffset, volcano.y - config.volcanoScale - YOffset, config.volcanoScale * 2, config.volcanoScale * 2);
            ctx.drawImage(volcano.lava, volcano.x - finalLavaScale - XOffset, volcano.y - finalLavaScale - YOffset, finalLavaScale * 2, finalLavaScale * 2);
        }

        // RENDER GAME OBJECTS:
        function renderGameObjects(layer, xOffset, yOffset) {
            let tmpSprite;
            let tmpX;
            let tmpY;
            gameObjects.forEach((tmp) => {
                tmpObj = tmp;
                if (tmpObj.alive) {
                    if (tmpObj.xWiggle) {
                        tmpObj.xWiggle *= math[3](0.99, delta);
                    }
                    if (tmpObj.yWiggle) {
                        tmpObj.yWiggle *= math[3](0.99, delta);
                    }

                    if (layer == 0) {
                        tmpObj.update(delta);
                    }
                    ctx.globalAlpha = tmpObj.alpha;
                    if (!tmpObj.active) {
                        tmpObj.alpha -= delta / 100;
                        if (tmpObj.alpha < 0) {
                            tmpObj.alive = false;
                            tmpObj.alpha = 0;
                        }
                    }
                    let tmpX = tmpObj.x + tmpObj.xWiggle - xOffset;
                    let tmpY = tmpObj.y + tmpObj.yWiggle - yOffset;
                    if (tmpObj.layer == layer && isOnScreen(tmpX, tmpY, tmpObj.scale + (tmpObj.blocker || 0))) {
                        if (tmpObj.isItem) {
                            let tmpSprite = getItemSprite(tmpObj);

                            ctx.save();
                            ctx.translate(tmpX, tmpY);
                            ctx.rotate(tmpObj.dir);
                            if (!tmpObj.active) {
                                ctx.scale(tmpObj.visScale / tmpObj.scale, tmpObj.visScale / tmpObj.scale);
                            }
                            if (tmpObj.hideFromEnemy) {
                                ctx.globalAlpha = 0.5
                            } else ctx.globalAlpha = 1;
                            ctx.drawImage(tmpSprite, -(tmpSprite.width / 2), -(tmpSprite.height / 2));

                            if (tmpObj.blocker) {
                                ctx.strokeStyle = "#db6e6e";
                                ctx.globalAlpha = 0.3;
                                ctx.lineWidth = 6;
                                renderCircle(0, 0, tmpObj.blocker, ctx, false, true);
                            }
                            ctx.restore();
                        } else {
                            if (tmpObj.type == 4) {
                                renderVolcano(xOffset, yOffset);
                            } else {
                                renderResSprite(tmpObj, tmpX, tmpY);
                            }
                        }
                    }

                }
            });
        }
        // RENDER MINIMAP:
        class MapPing {
            constructor(color, scale) {
                this.init = function (x, y) {
                    this.scale = 0;
                    this.x = x;
                    this.y = y;
                    this.active = true;
                };
                this.update = function (ctxt, delta) {
                    if (this.active) {
                        this.scale += 0.05 * delta;
                        if (this.scale >= scale) {
                            this.active = false;
                        } else {
                            ctxt.globalAlpha = (1 - Math.max(0, this.scale / scale));
                            ctxt.beginPath();
                            ctxt.arc((this.x / config.mapScale) * mapDisplay.width, (this.y / config.mapScale)
                                     * mapDisplay.width, this.scale, 0, 2 * Math.PI);
                            ctxt.stroke();
                        }
                    }
                };
                this.color = color;
            }
        }
        function pingMap(x, y) {
            tmpPing = mapPings.find(pings => !pings.active);
            if (!tmpPing) {
                tmpPing = new MapPing("#fff", config.mapPingScale);
                mapPings.push(tmpPing);
            }
            tmpPing.init(x, y);
        }
        function updateMapMarker() {
            mapMarker.x = player.x;
            mapMarker.y = player.y;
        }
        function renderMinimap(delta) {
            if (player && player.alive) {
                mapContext.clearRect(0, 0, mapDisplay.width, mapDisplay.height);

                // RENDER PINGS:
                mapContext.lineWidth = 4;
                for (let i = 0; i < mapPings.length; ++i) {
                    tmpPing = mapPings[i];
                    mapContext.strokeStyle = tmpPing.color;
                    tmpPing.update(mapContext, delta);
                }

                // RENDER BREAK TRACKS:
                mapContext.globalAlpha = 1;
                mapContext.fillStyle = "#ff0000";
                if (breakTrackers.length) {
                    mapContext.fillStyle = "#abcdef";
                    mapContext.font = "34px Hammersmith One";
                    mapContext.textBaseline = "middle";
                    mapContext.textAlign = "center";
                    for (let i = 0; i < breakTrackers.length;) {
                        mapContext.fillText("!", (breakTrackers[i].x/config.mapScale)*mapDisplay.width,
                                            (breakTrackers[i].y/config.mapScale)*mapDisplay.height);
                        i += 2;
                    }
                }

                // RENDER PLAYERS:
                mapContext.globalAlpha = 1;
                mapContext.fillStyle = "#fff";
                renderCircle((player.x/config.mapScale)*mapDisplay.width,
                             (player.y/config.mapScale)*mapDisplay.height, 7, mapContext, true);
                mapContext.fillStyle = "rgba(255,255,255,0.35)";
                if (player.team && minimapData) {
                    for (let i = 0; i < minimapData.length;) {
                        renderCircle((minimapData[i]/config.mapScale)*mapDisplay.width,
                                     (minimapData[i+1]/config.mapScale)*mapDisplay.height, 7, mapContext, true);
                        i+=2;
                    }
                }



                // DEATH LOCATION:
                if (lastDeath) {
                    mapContext.fillStyle = "#fc5553";
                    mapContext.font = "34px Hammersmith One";
                    mapContext.textBaseline = "middle";
                    mapContext.textAlign = "center";
                    mapContext.fillText("x", (lastDeath.x/config.mapScale)*mapDisplay.width,
                                        (lastDeath.y/config.mapScale)*mapDisplay.height);
                }

                // MAP MARKER:
                if (mapMarker) {
                    mapContext.fillStyle = "#fff";
                    mapContext.font = "34px Hammersmith One";
                    mapContext.textBaseline = "middle";
                    mapContext.textAlign = "center";
                    mapContext.fillText("x", (mapMarker.x/config.mapScale)*mapDisplay.width,
                                        (mapMarker.y/config.mapScale)*mapDisplay.height);
                }
            }
        }

        // ICONS:
        let iconSprites = {};
        let icons = ["crown", "skull"];
        function loadIcons() {
            icons.forEach((icon, index) => {
                let sprite = new Image();
                sprite.onload = sprite.isLoaded = true;
                sprite.src = `./../img/icons/${icon}.png`;
                iconSprites[index] = sprite;
            });
        }
        loadIcons();



        // UPDATE & ANIMATE:
        window.requestAnimFrame = function() {
            return null;
        }
        window.rAF = (function() {
            return window.requestAnimationFrame ||
                window.webkitRequestAnimationFrame ||
                window.mozRequestAnimationFrame ||
                function(callback) {
                window.setTimeout(callback, 1000 / 60);
            };
        })();

        window.prepareUI = function(tmpObj) {
            resize();
            // ACTION BAR:
            utils.removeAllChildren(actionBar);
            for (let i = 0; i < (items.weapons.length + items.list.length); ++i) {
                (function(i) {
                    utils.generateElement({
                        id: "actionBarItem" + i,
                        class: "actionBarItem",
                        style: "display:none",
                        onmouseout: function() {
                            showItemInfo();
                        },
                        parent: actionBar
                    });
                })(i);
            }
            for (let i = 0; i < (items.list.length + items.weapons.length); ++i) {
                (function(i) {
                    let tmpCanvas = document.createElement("canvas");
                    tmpCanvas.width = tmpCanvas.height = 66;
                    let tmpContext = tmpCanvas.getContext("2d");
                    tmpContext.translate((tmpCanvas.width / 2), (tmpCanvas.height / 2));
                    tmpContext.imageSmoothingEnabled = false;
                    tmpContext.webkitImageSmoothingEnabled = false;
                    tmpContext.mozImageSmoothingEnabled = false;
                    if (items.weapons[i]) {
                        tmpContext.rotate((Math.PI/4)+Math.PI);
                        let tmpSprite = new Image();
                        toolSprites[items.weapons[i].src] = tmpSprite;
                        tmpSprite.onload = function() {
                            this.isLoaded = true;
                            let tmpPad = 1 / (this.height / this.width);
                            let tmpMlt = (items.weapons[i].iPad || 1);
                            tmpContext.drawImage(this, -(tmpCanvas.width*tmpMlt*config.iconPad*tmpPad)/2, -(tmpCanvas.height*tmpMlt*config.iconPad)/2,
                                                 tmpCanvas.width*tmpMlt*tmpPad*config.iconPad, tmpCanvas.height*tmpMlt*config.iconPad);
                            tmpContext.fillStyle = "rgba(0, 0, 70, 0.1)";
                            tmpContext.globalCompositeOperation = "source-atop";
                            tmpContext.fillRect(-tmpCanvas.width / 2, -tmpCanvas.height / 2, tmpCanvas.width, tmpCanvas.height);
                            getEl('actionBarItem' + i).style.backgroundImage = "url(" + tmpCanvas.toDataURL() + ")";
                        };
                        tmpSprite.src = "./../img/weapons/" + items.weapons[i].src + ".png";
                        let tmpUnit = getEl('actionBarItem' + i);
                        tmpUnit.onmouseover = utils.checkTrusted(function() {
                            showItemInfo(items.weapons[i], true);
                        });
                        tmpUnit.onclick = utils.checkTrusted(function() {
                            soc.send("z", tmpObj.weapons[items.weapons[i].type]);
                        });
                        utils.hookTouchEvents(tmpUnit);
                    } else {
                        let tmpSprite = getItemSprite(items.list[i-items.weapons.length], true);
                        let tmpScale = Math.min(tmpCanvas.width - config.iconPadding, tmpSprite.width);
                        tmpContext.globalAlpha = 1;
                        tmpContext.drawImage(tmpSprite, -tmpScale / 2, -tmpScale / 2, tmpScale, tmpScale);
                        tmpContext.fillStyle = "rgba(0, 0, 70, 0.1)";
                        tmpContext.globalCompositeOperation = "source-atop";
                        tmpContext.fillRect(-tmpScale / 2, -tmpScale / 2, tmpScale, tmpScale);
                        getEl('actionBarItem' + i).style.backgroundImage = "url(" + tmpCanvas.toDataURL() + ")";
                        let tmpUnit = getEl('actionBarItem' + i);
                        tmpUnit.onmouseover = utils.checkTrusted(function() {
                            showItemInfo(items.list[i - items.weapons.length]);
                        });
                        tmpUnit.onclick = utils.checkTrusted(function() {
                            soc.send("z", tmpObj.items[tmpObj.getItemType(i - items.weapons.length)]);
                        });
                        utils.hookTouchEvents(tmpUnit);
                    }
                })(i);
            }
        };
        function addAlliance(data) {
            alliances.push(data);
        }

        function deleteAlliance(sid) {
            for (let i = alliances.length - 1; i >= 0; i--) {
                if (alliances[i].sid == sid) alliances.splice(i, 1);
            }
        }

        function allianceNotification(sid, name) {
            allianceNotifications.push({
                sid: sid,
                name: name
            });
        }
        function serverShutdownNotice(countdown) {}
        // PING SOCKET RESPONSE:
        function pingSocketResponse() {
            // ping.current = performance.now() - ping.last; // dont mind horribly messing it up
            pingDisplay.innerText = "Ping: " + window.pingTime + " ms | FPS " + utils.round(fpsTimer.ltime, 10);
        }


        // RENDER UI:
        function renderUI() {
            // MOVE CAMERA:
            if (player) {
                if (false) {
                    camX = player.x;
                    camY = player.y;
                } else {
                    let tmpDist = utils.getDistance(camX, camY, player.x, player.y);
                    let tmpDir = utils.getDirection(player.x, player.y, camX, camY);
                    let camSpd = Math.min(tmpDist * 0.01 * delta, tmpDist);
                    if (tmpDist > 0.05) {
                        camX += camSpd * Math.cos(tmpDir);
                        camY += camSpd * Math.sin(tmpDir);
                    } else {
                        camX = player.x;
                        camY = player.y;
                    }
                }
            } else {
                camX = config.mapScale / 2;
                camY = config.mapScale / 2;
            }

            // INTERPOLATE PLAYERS AND AI:
            let lastTime = now - (1000 / config.serverUpdateRate);
            let tmpDiff;
            for (let i = 0; i < players.length + ais.length; ++i) {
                tmpObj = players[i] || ais[i - players.length];
                if (tmpObj && tmpObj.visible) {
                    if (tmpObj.forcePos) {
                        tmpObj.x = tmpObj.x2;
                        tmpObj.y = tmpObj.y2;
                        tmpObj.dir = tmpObj.d2;
                    } else {
                        let total = tmpObj.t2 - tmpObj.t1;
                        let fraction = lastTime - tmpObj.t1;
                        let ratio = (fraction / total);
                        let rate = 170;
                        tmpObj.dt += delta;
                        let tmpRate = Math.min(1.7, tmpObj.dt / rate);
                        tmpDiff = (tmpObj.x2 - tmpObj.x1);
                        tmpObj.x = tmpObj.x1 + (tmpDiff * tmpRate);
                        tmpDiff = (tmpObj.y2 - tmpObj.y1);
                        tmpObj.y = tmpObj.y1 + (tmpDiff * tmpRate);

                        tmpObj.dir = Math.lerpAngle(tmpObj.d2, tmpObj.d1, Math.min(1.2, ratio));

                    }
                }
            }

            // RENDER CORDS:
            xOffset = camX - (screen.max.width / 2);
            yOffset = camY - (screen.max.height / 2);

            // RENDER BACKGROUND:
            if (config.snowBiomeTop - yOffset <= 0 && config.mapScale - config.snowBiomeTop - yOffset >= screen.max.height) {
                ctx.fillStyle = "#b6db66";
                ctx.fillRect(0, 0, screen.max.width, screen.max.height);
            } else if (config.mapScale - config.snowBiomeTop - yOffset <= 0) {
                ctx.fillStyle = "#dbc666";
                ctx.fillRect(0, 0, screen.max.width, screen.max.height);
            } else if (config.snowBiomeTop - yOffset >= screen.max.height) {
                ctx.fillStyle = "#fff";
                ctx.fillRect(0, 0, screen.max.width, screen.max.height);
            } else if (config.snowBiomeTop - yOffset >= 0) {
                ctx.fillStyle = "#fff";
                ctx.fillRect(0, 0, screen.max.width, config.snowBiomeTop - yOffset);
                ctx.fillStyle = "#b6db66";
                ctx.fillRect(0, config.snowBiomeTop - yOffset, screen.max.width,
                             screen.max.height - (config.snowBiomeTop - yOffset));
            } else {
                ctx.fillStyle = "#b6db66";
                ctx.fillRect(0, 0, screen.max.width,
                             (config.mapScale - config.snowBiomeTop - yOffset));
                ctx.fillStyle = "#dbc666";
                ctx.fillRect(0, (config.mapScale - config.snowBiomeTop - yOffset), screen.max.width,
                             screen.max.height - (config.mapScale - config.snowBiomeTop - yOffset));
            }

            // RENDER WATER AREAS:
            if (!firstSetup) {
                waterMult += waterPlus * config.waveSpeed * delta;
                if (waterMult >= config.waveMax) {
                    waterMult = config.waveMax;
                    waterPlus = -1;
                } else if (waterMult <= 1) {
                    waterMult = waterPlus = 1;
                }
                ctx.globalAlpha = 1;
                ctx.fillStyle = "#dbc666";
                renderWaterBodies(xOffset, yOffset, ctx, config.riverPadding);
                ctx.fillStyle = "#91b2db";
                renderWaterBodies(xOffset, yOffset, ctx, (waterMult - 1) * 250);
            }


            // RENDER GRID:
            /*ctx.lineWidth = 4;
            ctx.strokeStyle = "#000";
            ctx.globalAlpha = 0.1;
            ctx.beginPath();
            for (let x = -camX; x < screen.max.width; x += 60) {
                if (x > 0) {
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, screen.max.height);
                }
            }
            for (let y = -camY; y < screen.max.height; y += 60) {
                if (y > 0) {
                    ctx.moveTo(0, y);
                    ctx.lineTo(screen.max.width, y);
                }
            }
            ctx.stroke();*/

            // RENDER BOTTOM LAYER:
            ctx.globalAlpha = 1;
            ctx.strokeStyle = outlineColor;
            renderGameObjects(-1, xOffset, yOffset);

            // RENDER PROJECTILES:
            ctx.globalAlpha = 1;
            ctx.lineWidth = outlineWidth;
            renderProjectiles(0, xOffset, yOffset);

            // RENDER PLAYERS:
            renderPlayers(xOffset, yOffset, 0);

            // RENDER AI:
            ctx.globalAlpha = 1;
            for (let i = 0; i < ais.length; ++i) {
                tmpObj = ais[i];
                if (tmpObj.active && tmpObj.visible) {
                    tmpObj.animate(delta);
                    ctx.save();
                    ctx.translate(tmpObj.x - xOffset, tmpObj.y - yOffset);
                    ctx.rotate(tmpObj.dir + tmpObj.dirPlus - (Math.PI / 2));
                    renderAI(tmpObj, ctx);
                    ctx.restore();
                }
            }

            // RENDER GAME OBJECTS:
            renderGameObjects(0, xOffset, yOffset);
            renderGameObjects(1, xOffset, yOffset);
            renderGameObjects(2, xOffset, yOffset);
            renderGameObjects(3, xOffset, yOffset);


            // RENDER PLAYERS:
            renderPlayers(xOffset, yOffset, 1);

            // RENDER PROJECTILE:
            renderProjectiles(1, xOffset, yOffset);

            // MAP BOUNDARIES:
            ctx.fillStyle = "#000";
            ctx.globalAlpha = 0.09;
            if (xOffset <= 0) {
                ctx.fillRect(0, 0, -xOffset, screen.max.height);
            } if (config.mapScale - xOffset <= screen.max.width) {
                let tmpY = Math.max(0, -yOffset);
                ctx.fillRect(config.mapScale - xOffset, tmpY, screen.max.width - (config.mapScale - xOffset), screen.max.height - tmpY);
            } if (yOffset <= 0) {
                ctx.fillRect(-xOffset, 0, screen.max.width + xOffset, -yOffset);
            } if (config.mapScale - yOffset <= screen.max.height) {
                let tmpX = Math.max(0, -xOffset);
                let tmpMin = 0;
                if (config.mapScale - xOffset <= screen.max.width) tmpMin = screen.max.width - (config.mapScale - xOffset);
                ctx.fillRect(tmpX, config.mapScale - yOffset, (screen.max.width - tmpX) - tmpMin, screen.max.height - (config.mapScale - yOffset));
            }

            // RENDER DAY/NIGHT TIME:
            ctx.globalAlpha = 1;
            ctx.fillStyle = "rgba(0, 0, 70, 0.35)";
            ctx.fillRect(0, 0, screen.max.width, screen.max.height);

            // RENDER PLAYER AND AI UI:
            ctx.strokeStyle = darkOutlineColor;
            ctx.globalAlpha = 1;
            for (let i = 0; i < players.length + ais.length; ++i) {
                tmpObj = players[i] || ais[i - players.length];
                if (tmpObj.visible) {
                    ctx.strokeStyle = darkOutlineColor;

                    // NAME AND HEALTH:
                    if (tmpObj.skinIndex != 10 || (tmpObj==player) || (tmpObj.team && tmpObj.team==player.team)) {
                        let tmpText = (tmpObj.team?"["+tmpObj.team+"] ":"")+(tmpObj.name||"");
                        if (tmpText != "") {
                            let textX = tmpObj.x - xOffset;
                            let textY = tmpObj.y - yOffset - tmpObj.scale - config.nameY;
                            ctx.font = (tmpObj.nameScale||30) + "px Hammersmith One";
                            ctx.fillStyle = "#fff";
                            ctx.textBaseline = "middle";
                            ctx.textAlign = "center";
                            ctx.lineWidth = (tmpObj.nameScale?11:8);
                            ctx.lineJoin = "round";
                            ctx.strokeText(tmpText, tmpObj.x - xOffset, (tmpObj.y - yOffset - tmpObj.scale) - config.nameY);
                            ctx.fillText(tmpText, tmpObj.x - xOffset, (tmpObj.y - yOffset - tmpObj.scale) - config.nameY);
                            if (tmpObj.isLeader && iconSprites[0]?.isLoaded) {
                                let tmpS = config.crownIconScale;
                                let tmpX = textX - (tmpS / 2) - (ctx.measureText(tmpText).width / 2) - config.crownPad;
                                ctx.drawImage(iconSprites[0], tmpX, textY - (tmpS / 2) - 5, tmpS, tmpS);
                            }
                            if (tmpObj.iconIndex === 1 && iconSprites[1]?.isLoaded) {
                                let tmpS = config.crownIconScale;
                                let tmpX = textX + (tmpS / 2) + (ctx.measureText(tmpText).width / 2) + config.crownPad;
                                ctx.drawImage(iconSprites[1], tmpX, textY - (tmpS / 2) - 5, tmpS, tmpS);
                            }
                        }
                        if (tmpObj.health > 0) {

                            // HEALTH HOLDER:
                            ctx.fillStyle = darkOutlineColor;
                            ctx.roundRect(tmpObj.x - xOffset - config.healthBarWidth - config.healthBarPad,
                                          (tmpObj.y - yOffset + tmpObj.scale) + config.nameY, (config.healthBarWidth * 2) +
                                          (config.healthBarPad * 2), 17, 8);
                            ctx.fill();

                            // HEALTH BAR:
                            ctx.fillStyle = (tmpObj==player||(tmpObj.team&&tmpObj.team==player.team))?"#8ecc51":"#cc5151";
                            ctx.roundRect(tmpObj.x - xOffset - config.healthBarWidth,
                                          (tmpObj.y - yOffset + tmpObj.scale) + config.nameY + config.healthBarPad,
                                          ((config.healthBarWidth * 2) * (tmpObj.health / tmpObj.maxHealth)), 17 - config.healthBarPad * 2, 7);
                            ctx.fill();
                        }
                    }
                }
            }

            ctx.globalAlpha = 1;

            // RENDER CHAT MESSAGES:
            let emojis = {
                joy: "😂",
                sob: "😭",
                sus: "🤨",
                kiss: "😘",
                omg: "😲",
                "500IQ": "🤯",
                pls: "🥺",
                horny: "🥵",
                cold: "🥶",
                cry: "😢",
                sorry: "😓",
                yummy: "😋",
                angry: "😡",
                skull: "💀",
                dizzy: "🥴",
                party: "🥳",
                ez: "😎",
                wink: "😉",
                flushed: "😳",
                thumbsup: "👍",
            };
            for (let i = 0; i < players.length; ++i) {
                let player = players[i];
                if (
                    player.visible &&
                    player.chatMessages &&
                    player.chatMessages.length > 0
                ) {
                    let tmpX = player.x - xOffset;
                    let baseY = player.y - player.scale - yOffset - 90;
                    let yOffsetIncrement = 50;
                    for (let j = 0; j < player.chatMessages.length; j++) {
                        let chatObj = player.chatMessages[j];
                        let chatMessage = chatObj.message;
                        let tmpY =
                            baseY - (player.chatMessages.length - 1 - j) * yOffsetIncrement;
                        if (Date.now() - chatObj.time > 5000) {
                            player.chatMessages.splice(j, 1);
                            j--;
                            continue;
                        }
                        ctx.font = "32px Hammersmith One";
                        let tmpSize = ctx.measureText(chatMessage);
                        ctx.textBaseline = "middle";
                        ctx.textAlign = "center";
                        let tmpH = 47;
                        let tmpW = tmpSize.width + 17;
                        ctx.fillStyle = "rgba(0,0,0,0.2)";
                        ctx.roundRect(tmpX - tmpW / 2, tmpY - tmpH / 2, tmpW, tmpH, 6);
                        ctx.fill();
                        ctx.fillStyle = "#e3e3e3";
                        for (let e in emojis) {
                            chatMessage = chatMessage.replaceAll(":" + e + ":", emojis[e]);
                        }

                        ctx.fillText(chatMessage, tmpX, tmpY);
                    }
                }
            }

            // SHOW TEXTS:
            renderTexts(delta);


            ctx.globalAlpha = 1;

            // RENDER MINIMAP:
            renderMinimap(delta);
        }

        // DO UPDATE:
        function doUpdate() {
            now = performance.now();
            delta = now - lastUpdate;
            lastUpdate = now;
            let timer = performance.now();
            let diff = timer - fpsTimer.last;
            if (diff >= 1000) {
                fpsTimer.ltime = fpsTimer.time * (1000 / diff);
                fpsTimer.last = timer;
                fpsTimer.time = 0;
            }
            fpsTimer.time++;
            renderUI();
            window.rAF(doUpdate);
        }
        prepareMenuBackground();
        doUpdate();









        // CONNECT SERVER:
        soc.connect(function(error) {
            if (error) disconnect(error);
        }, {
            A: setInitData,
            B: disconnect,
            C: setupGame,
            D: addPlayer,
            E: removePlayer,
            a: updatePlayers,
            G: updateLeaderboard,
            H: loadGameObject,
            I: loadAI,
            J: animateAI,
            K: gatherAnimation,
            L: wiggleGameObject,
            M: shootTurret,
            N: updatePlayerValue,
            O: updateHealth,
            P: killPlayer,
            Q: killObject,
            R: killObjects,
            S: updateItemCounts,
            T: updateAge,
            U: updateUpgrades,
            V: updateItems,
            X: addProjectile,
            Y: remProjectile,
            Z: serverShutdownNotice,
            g: addAlliance,
            1: deleteAlliance,
            2: allianceNotification,
            3: setPlayerTeam,
            4: setAlliancePlayers,
            5: updateStoreItems,
            6: receiveChat,
            7: updateMinimap,
            8: showText,
            9: pingMap,
            0: pingSocketResponse,
        });
    },
    "./modules/uis.js": function(module, exports) {
        let e = (id) => document.getElementById(id);
        module.exports = {
            ads: {
                adCard: e("adCard"),
                adContainer: e("ad-container"),
                promoImg: e("promoImg"),
                promoImageHolder: e("promoImgHolder"),
                wideAdCard: e("wideAdCard"),
            },
            buttons: {
                altchaCheck: e("altcha_checkbox"),
                store: e("storeButton"),
                alliance: e("allianceButton"),
                chat: e("chatButton"),
                enterGame: e("enterGame"),
                partyButton: e("partyButton"),
                joinB: e("joinPartyButton"),
                settingsButton: e("settingsButton"),
                settingsButtonTitle: e("settingsButton").getElementsByTagName('span')[0],
            },
            resources: {
                food: e("foodDisplay"),
                wood: e("woodDisplay"),
                stone: e("stoneDisplay"),
                score: e("scoreDisplay"),
                kill: e("killCounter"),
            },
            global: {// i made this since im to lazy to catagorize for them
                altcha: e("altcha"),
                menuText: e("desktopInstructions"),
                setupCard: e("setupCard"),
                guideCard: e("guideCard"),
                gameUI: e("gameUI"),
                gameName: e("gameName"),
                mainMenu: e("mainMenu"),
                storeMenu: e("storeMenu"),
                nameInput: e("nameInput"),
                gameCanvas: e("gameCanvas"),
                gameContext: e("gameCanvas").getContext("2d"),
                mapDisplay: e("mapDisplay"),
                mapContext: e("mapDisplay").getContext("2d"),
                shutdownDisplay: e("shutdownDisplay"),
                pingDisplay: e("pingDisplay"),
                loadingText: e("loadingText"),
                diedText: e("diedText"),
                ageText: e("ageText"),
                ageBarBody: e("ageBarBody"),
                allianceMenu: e("allianceMenu"),
                allianceManager: e("allianceManager"),
                notificationDisplay: e("notificationDisplay"),
                leaderboardData: e("leaderboardData"),
                actionBar: e("actionBar"),
                playMusic: e("playMusic"),
                upgradeCounter: e("upgradeCounter"),
                chatBox: e("chatBox"),
            },
            holder: {
                menuCardHolder: e("menuCardHolder"),
                itemInfoHolder: e("itemInfoHolder"),
                upgradeHolder: e("upgradeHolder"),
                allianceHolder: e("allianceHolder"),
                skinColorHolder: e("skinColorHolder"),
                storeHolder: e("storeHolder"),
                chatHolder: e("chatHolder"),
            },
            server: {
                serverBrowser: e("serverBrowser"),
                nativeResolutionOption: e("nativeResolution"),
                showPingOption: e("showPing"),
            },
        };
    },
    "./modules/config.js": function(module, exports) {
        // RENDER:
        module.exports.maxScreenWidth = 1920;
        module.exports.maxScreenHeight = 1080;

        // SERVER:
        module.exports.serverUpdateRate = 9;
        module.exports.maxPlayers = 40;
        module.exports.maxPlayersHard = module.exports.maxPlayers + 10;
        module.exports.collisionDepth = 6;
        module.exports.minimapRate = 3000;

        // COLLISIONS:
        module.exports.colGrid = 10;

        // CLIENT:
        module.exports.clientSendRate = 5;

        // UI:
        module.exports.healthBarWidth = 50;
        module.exports.healthBarPad = 4.5;
        module.exports.iconPadding = 15;
        module.exports.iconPad = 0.9;
        module.exports.deathFadeout = 3000;
        module.exports.crownIconScale = 60;
        module.exports.crownPad = 35;

        // CHAT:
        module.exports.chatCountdown = 3000;
        module.exports.chatCooldown = 500;

        // SANDBOX:
        module.exports.isSandbox = window.location.hostname == "sandbox.moomoo.io" ? true : false;

        // PLAYER:
        module.exports.maxAge = 100;
        module.exports.gatherAngle = Math.PI/2.6;
        module.exports.gatherWiggle = 10;
        module.exports.hitReturnRatio = 0.25;
        module.exports.hitAngle = Math.PI / 2;
        module.exports.playerScale = 35;
        module.exports.playerSpeed = 0.0016;
        module.exports.playerDecel = 0.993;
        module.exports.nameY = 34;

        // CUSTOMIZATION:
        module.exports.skinColors = ["#bf8f54", "#cbb091", "#896c4b",
                                     "#fadadc", "#ececec", "#c37373", "#4c4c4c", "#ecaff7", "#738cc3",
                                     "#8bc373"];

        // ANIMALS:
        module.exports.animalCount = 7;
        module.exports.aiTurnRandom = 0.06;
        module.exports.cowNames = ["Sid", "Steph", "Bmoe", "Romn", "Jononthecool", "Fiona", "Vince", "Nathan", "Nick", "Flappy", "Ronald", "Otis", "Pepe", "Mc Donald", "Theo", "Fabz", "Oliver", "Jeff", "Jimmy", "Helena", "Reaper",
                                   "Ben", "Alan", "Naomi", "XYZ", "Clever", "Jeremy", "Mike", "Destined", "Stallion", "Allison", "Meaty", "Sophia", "Vaja", "Joey", "Pendy", "Murdoch", "Theo", "Jared", "July", "Sonia", "Mel", "Dexter", "Quinn", "Milky"];

        // WEAPONS:
        module.exports.shieldAngle = Math.PI/3;
        module.exports.weaponVariants = [{
            id: 0,
            src: "",
            xp: 0,
            val: 1
        }, {
            id: 1,
            src: "_g",
            xp: 3000,
            val: 1.1
        }, {
            id: 2,
            src: "_d",
            xp: 7000,
            val: 1.18
        }, {
            id: 3,
            src: "_r",
            poison: true,
            xp: 12000,
            val: 1.18
        }];
        module.exports.fetchVariant = function(player) {
            var tmpXP = player.weaponXP[player.weaponIndex]||0;
            for (var i = module.exports.weaponVariants.length - 1; i >= 0; --i) {
                if (tmpXP >= module.exports.weaponVariants[i].xp) {
                    return module.exports.weaponVariants[i];
                }
            }
        };

        // NATURE:
        module.exports.resourceTypes = ["wood", "food", "stone", "points"];
        module.exports.areaCount = 7;
        module.exports.treesPerArea = 9;
        module.exports.bushesPerArea = 3;
        module.exports.totalRocks = 32;
        module.exports.goldOres = 7;
        module.exports.riverWidth = 724;
        module.exports.riverPadding = 114;
        module.exports.waterCurrent = 0.0011;
        module.exports.waveSpeed = 0.0001;
        module.exports.waveMax = 1.3;
        module.exports.treeScales = [150, 160, 165, 175];
        module.exports.bushScales = [80, 85, 95];
        module.exports.rockScales = [80, 85, 90];

        // BIOME DATA:
        module.exports.snowBiomeTop = 2400;
        module.exports.snowSpeed = 0.75;

        // DATA:
        module.exports.maxNameLength = 15;

        // MAP:
        module.exports.mapScale = 14400;
        module.exports.mapPingScale = 40;
        module.exports.mapPingTime = 2200;

        // VOLCANO:
        module.exports.volcanoScale = 320;
        module.exports.innerVolcanoScale = 100;
        module.exports.volcanoAnimalStrength = 2;
        module.exports.volcanoAnimationDuration = 3200;
        module.exports.volcanoAggressionRadius = 1440;
        module.exports.volcanoAggressionPercentage = 0.2;
        module.exports.volcanoDamagePerSecond = -1;
        module.exports.volcanoLocationX = 14400 - 440;
        module.exports.volcanoLocationY = 14400 - 440;
    },
    "./modules/utils.js": function(module, exports) {
        let _this = this;
        let math = [// i use it as an array cause im a nigger :>
            Math.PI, // 0
            Math.abs, // 1
            Math.sqrt, // 2
            Math.pow, // 3
            Math.round, // 4
            Math.floor, // 5
            Math.ceil, // 6
            Math.max, // 7
            Math.min, // 8
            Math.random, // 9
            Math.sin, // 10
            Math.cos, // 11
            Math.tan, // 12
            Math.log, // 13
            Math.exp, // 14
            Math.sign, // 15
            Math.atan2, // 16
        ];
        module.exports.round = function(num, pos) {
            return math[4](num * pos) / pos;
        };

        module.exports.toRad = function(angle) {
            return angle * (math[0] / 180);
        };

        module.exports.toAng = function(rad) {
            return rad / (math[0] / 180);
        };

        module.exports.randInt = function(min, max) {
            return math[5](math[9]() * (max - min + 1)) + min;
        };

        module.exports.randFloat = function(min, max) {
            return math[9]() * (max - min) + min;
        };

        module.exports.randChoose = function(array) {
            return array[module.exports.randInt(0, array.length - 1)];
        };

        module.exports.fixCap = function(num, min, max) {
            return math[7](min, math[8](max, num));
        };

        module.exports.lerp = function(v1, v2, amount) {
            return v1 + (v2 - v1) * amount;
        };

        module.exports.decel = function(val, cel) {
            if (val > 0) {
                val = math[8](0, val - cel);
            } else if (val < 0) {
                val = math[7](0, val + cel);
            }
            return val;
        };

        module.exports.getDistance = function(x1, y1, x2, y2) {
            let dx = x1 - x2;
            let dy = y1 - y2;
            return math[2]((dx * dx) + (dy * dy));
        };

        module.exports.getDirection = function(x1, y1, x2, y2) {
            let dx = x1 - x2;
            let dy = y1 - y2;
            return math[16](dy, dx);
        };
        module.exports.getDist = function(ins, oth, t1 = "", t2 = "") {
            let dx = ins["x" + t1] - oth["x" + t2];
            let dy = ins["y" + t1] - oth["y" + t2];
            return math[2]((dx * dx) + (dy * dy));
        };

        module.exports.getDirect = function(ins, oth, t1 = "", t2 = "") {
            let dx = ins["x" + t1] - oth["x" + t2];
            let dy = ins["y" + t1] - oth["y" + t2];
            return math[16](dy, dx);
        };

        module.exports.getAngleDist = function(a, b) {
            let p = math[1](b - a) % (math[0] * 2);
            return (p > math[0] ? (math[0] * 2) - p : p);
        };

        module.exports.isNumber = function(n) {
            return (typeof n == "number" && !isNaN(n) && isFinite(n));
        };

        module.exports.isString = function(s) {
            return (s && typeof s == "string");
        };

        module.exports.kFormat = function(num) {
            return num > 999 ? (num / 1000).toFixed(1) + 'k' : num;
        };

        module.exports.sFormat = function(num) {
            let fixs = [
                {num: 1e3, string: "k"},
                {num: 1e6, string: "m"},
                {num: 1e9, string: "b"},
                {num: 1e12, string: "q"}
            ].reverse();
            let sp = fixs.find(v => num >= v.num);
            return sp ? (num / sp.num).toFixed(1) + sp.string : num;
        };

        module.exports.capitalizeFirst = function(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        };

        module.exports.fixTo = function(n, v) {
            return parseFloat(n.toFixed(v));
        };

        module.exports.sortByPoints = function(a, b) {
            return parseFloat(b.points) - parseFloat(a.points);
        };

        module.exports.lineInRect = function(recX, recY, recX2, recY2, x1, y1, x2, y2) {
            let minX = math[8](x1, x2), maxX = math[7](x1, x2);
            if (maxX > recX2) maxX = recX2;
            if (minX < recX) minX = recX;
            if (minX > maxX) return false;
            let minY = y1, maxY = y2, dx = x2 - x1;
            if (math[1](dx) > 0.0000001) {
                let a = (y2 - y1) / dx, b = y1 - a * x1;
                minY = a * minX + b;
                maxY = a * maxX + b;
            }
            if (minY > maxY) [minY, maxY] = [maxY, minY];
            if (maxY > recY2) maxY = recY2;
            if (minY < recY) minY = recY;
            return minY <= maxY;
        };

        module.exports.containsPoint = function(element, x, y) {
            let bounds = element.getBoundingClientRect();
            let left = bounds.left + window.scrollX;
            let top = bounds.top + window.scrollY;
            return x > left && x < left + bounds.width && y > top && y < top + bounds.height;
        };

        module.exports.mousifyTouchEvent = function(event) {
            let touch = event.changedTouches[0];
            event.screenX = touch.screenX;
            event.screenY = touch.screenY;
            event.clientX = touch.clientX;
            event.clientY = touch.clientY;
            event.pageX = touch.pageX;
            event.pageY = touch.pageY;
        };

        module.exports.hookTouchEvents = function (element, skipPrevent) {
            const preventDefault = !skipPrevent;
            let isHovering = false;
            const passive = false;

            element.addEventListener("touchstart", this.checkTrusted(touchStart), passive);
            element.addEventListener("touchmove", this.checkTrusted(touchMove), passive);
            element.addEventListener("touchend", this.checkTrusted(touchEnd), passive);
            element.addEventListener("touchcancel", this.checkTrusted(touchEnd), passive);
            element.addEventListener("touchleave", this.checkTrusted(touchEnd), passive);

            function touchStart(e) {
                _this.mousifyTouchEvent(e);
                window.setUsingTouch(true);
                if (preventDefault) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                if (element.onmouseover) element.onmouseover(e);
                isHovering = true;
            }

            function touchMove(e) {
                _this.mousifyTouchEvent(e);
                window.setUsingTouch(true);
                if (preventDefault) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                if (_this.containsPoint(element, e.pageX, e.pageY)) {
                    if (!isHovering) {
                        if (element.onmouseover) element.onmouseover(e);
                        isHovering = true;
                    }
                } else {
                    if (isHovering) {
                        if (element.onmouseout) element.onmouseout(e);
                        isHovering = false;
                    }
                }
            }

            function touchEnd(e) {
                _this.mousifyTouchEvent(e);
                window.setUsingTouch(true);
                if (preventDefault) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                if (isHovering) {
                    if (element.onclick) element.onclick(e);
                    if (element.onmouseout) element.onmouseout(e);
                    isHovering = false;
                }
            }
        };

        module.exports.removeAllChildren = function (element) {
            while (element.hasChildNodes()) {
                element.removeChild(element.lastChild);
            }
        };

        module.exports.generateElement = function (config) {
            const element = document.createElement(config.tag || "div");

            const bind = (configValue, elementValue) => {
                if (config[configValue]) {
                    element[elementValue] = config[configValue];
                }
            };

            bind("text", "textContent");
            bind("html", "innerHTML");
            bind("class", "className");
            for (let key in config) {
                if (!["tag", "text", "html", "class", "style", "hookTouch", "parent", "children"].includes(key)) {
                    element[key] = config[key];
                }
            }
            if (element.onclick) element.onclick = this.checkTrusted(element.onclick);
            if (element.onmouseover) element.onmouseover = this.checkTrusted(element.onmouseover);
            if (element.onmouseout) element.onmouseout = this.checkTrusted(element.onmouseout);
            if (config.style) element.style.cssText = config.style;
            if (config.hookTouch) this.hookTouchEvents(element);
            if (config.parent) config.parent.appendChild(element);
            if (config.children) {
                config.children.forEach(child => element.appendChild(child));
            }

            return element;
        };

        module.exports.eventIsTrusted = function (ev) {
            return ev && typeof ev.isTrusted === "boolean" ? ev.isTrusted : true;
        };

        module.exports.checkTrusted = function (callback) {
            return function (ev) {
                if (ev && ev instanceof Event && (ev && typeof ev.isTrusted == "boolean" ? ev.isTrusted : true)) {
                    callback(ev);
                } else {
                    console.error("Event is not trusted.", ev);
                }
            };
        };

        module.exports.randomString = function (length) {
            let text = "";
            const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            for (let i = 0; i < length; i++) {
                text += possible.charAt(Math.floor(Math.random() * possible.length));
            }
            return text;
        };

        module.exports.randomCodeStr = function (length, len2) {
            let str = "";
            for (let i = 0; i < length; i++) {
                str += String.fromCharCode(module.exports.randInt(0, len2));
            }
            return str;
        };

        module.exports.countInArray = function(array, val) {
            return array.reduce((count, item) => count + (item === val ? 1 : 0), 0);
        };
    },
    "./modules/items.js": function(module, exports) {
        // ITEM GROUPS:
        module.exports.groups = [{
            id: 0,
            name: "food",
            layer: 0
        }, {
            id: 1,
            name: "walls",
            place: true,
            limit: 30,
            layer: 0
        }, {
            id: 2,
            name: "spikes",
            place: true,
            limit: 15,
            layer: 0
        }, {
            id: 3,
            name: "mill",
            place: true,
            limit: 7,
            layer: 1
        }, {
            id: 4,
            name: "mine",
            place: true,
            limit: 1,
            layer: 0
        }, {
            id: 5,
            name: "trap",
            place: true,
            limit: 6,
            layer: -1
        }, {
            id: 6,
            name: "booster",
            place: true,
            limit: 12,
            layer: -1
        }, {
            id: 7,
            name: "turret",
            place: true,
            limit: 2,
            layer: 1
        }, {
            id: 8,
            name: "watchtower",
            place: true,
            limit: 12,
            layer: 1
        }, {
            id: 9,
            name: "buff",
            place: true,
            limit: 4,
            layer: -1
        }, {
            id: 10,
            name: "spawn",
            place: true,
            limit: 1,
            layer: -1
        }, {
            id: 11,
            name: "sapling",
            place: true,
            limit: 2,
            layer: 0
        }, {
            id: 12,
            name: "blocker",
            place: true,
            limit: 3,
            layer: -1
        }, {
            id: 13,
            name: "teleporter",
            place: true,
            limit: 2,
            layer: -1
        }];

        // PROJECTILES:
        exports.projectiles = [{
            indx: 0,
            layer: 0,
            src: "arrow_1",
            dmg: 25,
            speed: 1.6,
            scale: 103,
            range: 1000
        }, {
            indx: 1,
            layer: 1,
            dmg: 25,
            scale: 20
        }, {
            indx: 0,
            layer: 0,
            src: "arrow_1",
            dmg: 35,
            speed: 2.5,
            scale: 103,
            range: 1200
        }, {
            indx: 0,
            layer: 0,
            src: "arrow_1",
            dmg: 30,
            speed: 2,
            scale: 103,
            range: 1200
        }, {
            indx: 1,
            layer: 1,
            dmg: 16,
            scale: 20
        }, {
            indx: 0,
            layer: 0,
            src: "bullet_1",
            dmg: 50,
            speed: 3.6,
            scale: 160,
            range: 1400
        }];

        // WEAPONS:
        exports.weapons = [{
            id: 0,
            type: 0,
            name: "tool hammer",
            desc: "tool for gathering all resources",
            src: "hammer_1",
            length: 140,
            width: 140,
            xOff: -3,
            yOff: 18,
            dmg: 25,
            range: 65,
            gather: 1,
            speed: 300
        }, {
            id: 1,
            type: 0,
            age: 2,
            name: "hand axe",
            desc: "gathers resources at a higher rate",
            src: "axe_1",
            length: 140,
            width: 140,
            xOff: 3,
            yOff: 24,
            dmg: 30,
            spdMult: 1,
            range: 70,
            gather: 2,
            speed: 400
        }, {
            id: 2,
            type: 0,
            age: 8,
            pre: 1,
            name: "great axe",
            desc: "deal more damage and gather more resources",
            src: "great_axe_1",
            length: 140,
            width: 140,
            xOff: -8,
            yOff: 25,
            dmg: 35,
            spdMult: 1,
            range: 75,
            gather: 4,
            speed: 400
        }, {
            id: 3,
            type: 0,
            age: 2,
            name: "short sword",
            desc: "increased attack power but slower move speed",
            src: "sword_1",
            iPad: 1.3,
            length: 130,
            width: 210,
            xOff: -8,
            yOff: 46,
            dmg: 35,
            spdMult: 0.85,
            range: 110,
            gather: 1,
            speed: 300
        }, {
            id: 4,
            type: 0,
            age: 8,
            pre: 3,
            name: "katana",
            desc: "greater range and damage",
            src: "samurai_1",
            iPad: 1.3,
            length: 130,
            width: 210,
            xOff: -8,
            yOff: 59,
            dmg: 40,
            spdMult: 0.8,
            range: 118,
            gather: 1,
            speed: 300
        }, {
            id: 5,
            type: 0,
            age: 2,
            name: "polearm",
            desc: "long range melee weapon",
            src: "spear_1",
            iPad: 1.3,
            length: 130,
            width: 210,
            xOff: -8,
            yOff: 53,
            dmg: 45,
            knock: 0.2,
            spdMult: 0.82,
            range: 142,
            gather: 1,
            speed: 700
        }, {
            id: 6,
            type: 0,
            age: 2,
            name: "bat",
            desc: "fast long range melee weapon",
            src: "bat_1",
            iPad: 1.3,
            length: 110,
            width: 180,
            xOff: -8,
            yOff: 53,
            dmg: 20,
            knock: 0.7,
            range: 110,
            gather: 1,
            speed: 300
        }, {
            id: 7,
            type: 0,
            age: 2,
            name: "daggers",
            desc: "really fast short range weapon",
            src: "dagger_1",
            iPad: 0.8,
            length: 110,
            width: 110,
            xOff: 18,
            yOff: 0,
            dmg: 20,
            knock: 0.1,
            range: 65,
            gather: 1,
            hitSlow: 0.1,
            spdMult: 1.13,
            speed: 100
        }, {
            id: 8,
            type: 0,
            age: 2,
            name: "stick",
            desc: "great for gathering but very weak",
            src: "stick_1",
            length: 140,
            width: 140,
            xOff: 3,
            yOff: 24,
            dmg: 1,
            spdMult: 1,
            range: 70,
            gather: 7,
            speed: 400
        }, {
            id: 9,
            type: 1,
            age: 6,
            name: "hunting bow",
            desc: "bow used for ranged combat and hunting",
            src: "bow_1",
            req: ["wood", 4],
            length: 120,
            width: 120,
            xOff: -6,
            yOff: 0,
            projectile: 0,
            spdMult: 0.75,
            speed: 600
        }, {
            id: 10,
            type: 1,
            age: 6,
            name: "great hammer",
            desc: "hammer used for destroying structures",
            src: "great_hammer_1",
            length: 140,
            width: 140,
            xOff: -9,
            yOff: 25,
            dmg: 10,
            spdMult: 0.88,
            range: 75,
            sDmg: 7.5,
            gather: 1,
            speed: 400
        }, {
            id: 11,
            type: 1,
            age: 6,
            name: "wooden shield",
            desc: "blocks projectiles and reduces melee damage",
            src: "shield_1",
            length: 120,
            width: 120,
            shield: 0.2,
            xOff: 6,
            yOff: 0,
            spdMult: 0.7
        }, {
            id: 12,
            type: 1,
            age: 8,
            pre: 9,
            name: "crossbow",
            desc: "deals more damage and has greater range",
            src: "crossbow_1",
            req: ["wood", 5],
            aboveHand: true,
            armS: 0.75,
            length: 120,
            width: 120,
            xOff: -4,
            yOff: 0,
            projectile: 2,
            spdMult: 0.7,
            speed: 700
        }, {
            id: 13,
            type: 1,
            age: 9,
            pre: 12,
            name: "repeater crossbow",
            desc: "high firerate crossbow with reduced damage",
            src: "crossbow_2",
            req: ["wood", 10],
            aboveHand: true,
            armS: 0.75,
            length: 120,
            width: 120,
            xOff: -4,
            yOff: 0,
            projectile: 3,
            spdMult: 0.7,
            speed: 230
        }, {
            id: 14,
            type: 1,
            age: 6,
            name: "mc grabby",
            desc: "steals resources from enemies",
            src: "grab_1",
            length: 130,
            width: 210,
            xOff: -8,
            yOff: 53,
            dmg: 0,
            steal: 250,
            knock: 0.2,
            spdMult: 1.05,
            range: 125,
            gather: 0,
            speed: 700
        }, {
            id: 15,
            type: 1,
            age: 9,
            pre: 12,
            name: "musket",
            desc: "slow firerate but high damage and range",
            src: "musket_1",
            req: ["stone", 10],
            aboveHand: true,
            rec: 0.35,
            armS: 0.6,
            hndS: 0.3,
            hndD: 1.6,
            length: 205,
            width: 205,
            xOff: 25,
            yOff: 0,
            projectile: 5,
            hideProjectile: true,
            spdMult: 0.6,
            speed: 1500
        }];

        // ITEMS:
        module.exports.list = [{
            group: module.exports.groups[0],
            name: "apple",
            desc: "restores 20 health when consumed",
            req: ["food", 10],
            consume: function(doer) {
                return doer.changeHealth(20, doer);
            },
            scale: 22,
            healing: 20,
            holdOffset: 15
        }, {
            age: 3,
            group: module.exports.groups[0],
            name: "cookie",
            desc: "restores 40 health when consumed",
            req: ["food", 15],
            consume: function(doer) {
                return doer.changeHealth(40, doer);
            },
            healing: 40,
            scale: 27,
            holdOffset: 15
        }, {
            age: 7,
            group: module.exports.groups[0],
            name: "cheese",
            desc: "restores 30 health and another 50 over 5 seconds",
            req: ["food", 25],
            consume: function(doer) {
                if (doer.changeHealth(30, doer) || doer.health < 100) {
                    doer.dmgOverTime.dmg = -10;
                    doer.dmgOverTime.doer = doer;
                    doer.dmgOverTime.time = 5;
                    return true;
                }
                return false;
            },
            healing: 30,
            scale: 27,
            holdOffset: 15
        }, {
            group: module.exports.groups[1],
            name: "wood wall",
            desc: "provides protection for your village",
            req: ["wood", 10],
            projDmg: true,
            health: 380,
            scale: 50,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 3,
            group: module.exports.groups[1],
            name: "stone wall",
            desc: "provides improved protection for your village",
            req: ["stone", 25],
            health: 900,
            scale: 50,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 7,
            pre: 1,
            group: module.exports.groups[1],
            name: "castle wall",
            desc: "provides powerful protection for your village",
            req: ["stone", 35],
            health: 1500,
            scale: 52,
            holdOffset: 20,
            placeOffset: -5
        }, {
            group: module.exports.groups[2],
            name: "spikes",
            desc: "damages enemies when they touch them",
            req: ["wood", 20, "stone", 5],
            health: 400,
            dmg: 20,
            scale: 49,
            spritePadding: -23,
            holdOffset: 8,
            placeOffset: -5
        }, {
            age: 5,
            group: module.exports.groups[2],
            name: "greater spikes",
            desc: "damages enemies when they touch them",
            req: ["wood", 30, "stone", 10],
            health: 500,
            dmg: 35,
            scale: 52,
            spritePadding: -23,
            holdOffset: 8,
            placeOffset: -5
        }, {
            age: 9,
            pre: 1,
            group: module.exports.groups[2],
            name: "poison spikes",
            desc: "poisons enemies when they touch them",
            req: ["wood", 35, "stone", 15],
            health: 600,
            dmg: 30,
            pDmg: 5,
            scale: 52,
            spritePadding: -23,
            holdOffset: 8,
            placeOffset: -5
        }, {
            age: 9,
            pre: 2,
            group: module.exports.groups[2],
            name: "spinning spikes",
            desc: "damages enemies when they touch them",
            req: ["wood", 30, "stone", 20],
            health: 500,
            dmg: 45,
            turnSpeed: 0.003,
            scale: 52,
            spritePadding: -23,
            holdOffset: 8,
            placeOffset: -5
        }, {
            group: module.exports.groups[3],
            name: "windmill",
            desc: "generates gold over time",
            req: ["wood", 50, "stone", 10],
            health: 400,
            pps: 1,
            turnSpeed: 0.0016,
            spritePadding: 25,
            iconLineMult: 12,
            scale: 45,
            holdOffset: 20,
            placeOffset: 5
        }, {
            age: 5,
            pre: 1,
            group: module.exports.groups[3],
            name: "faster windmill",
            desc: "generates more gold over time",
            req: ["wood", 60, "stone", 20],
            health: 500,
            pps: 1.5,
            turnSpeed: 0.0025,
            spritePadding: 25,
            iconLineMult: 12,
            scale: 47,
            holdOffset: 20,
            placeOffset: 5
        }, {
            age: 8,
            pre: 1,
            group: module.exports.groups[3],
            name: "power mill",
            desc: "generates more gold over time",
            req: ["wood", 100, "stone", 50],
            health: 800,
            pps: 2,
            turnSpeed: 0.005,
            spritePadding: 25,
            iconLineMult: 12,
            scale: 47,
            holdOffset: 20,
            placeOffset: 5
        }, {
            age: 5,
            group: module.exports.groups[4],
            type: 2,
            name: "mine",
            desc: "allows you to mine stone",
            req: ["wood", 20, "stone", 100],
            iconLineMult: 12,
            scale: 65,
            holdOffset: 20,
            placeOffset: 0
        }, {
            age: 5,
            group: module.exports.groups[11],
            type: 0,
            name: "sapling",
            desc: "allows you to farm wood",
            req: ["wood", 150],
            iconLineMult: 12,
            colDiv: 0.5,
            scale: 110,
            holdOffset: 50,
            placeOffset: -15
        }, {
            age: 4,
            group: module.exports.groups[5],
            name: "pit trap",
            desc: "pit that traps enemies if they walk over it",
            req: ["wood", 30, "stone", 30],
            trap: true,
            ignoreCollision: true,
            hideFromEnemy: true,
            health: 500,
            colDiv: 0.2,
            scale: 50,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 4,
            group: module.exports.groups[6],
            name: "boost pad",
            desc: "provides boost when stepped on",
            req: ["stone", 20, "wood", 5],
            ignoreCollision: true,
            boostSpeed: 1.5,
            health: 150,
            colDiv: 0.7,
            scale: 45,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 7,
            group: module.exports.groups[7],
            doUpdate: true,
            name: "turret",
            desc: "defensive structure that shoots at enemies",
            req: ["wood", 200, "stone", 150],
            health: 800,
            projectile: 1,
            shootRange: 700,
            shootRate: 2200,
            scale: 43,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 7,
            group: module.exports.groups[8],
            name: "platform",
            desc: "platform to shoot over walls and cross over water",
            req: ["wood", 20],
            ignoreCollision: true,
            zIndex: 1,
            health: 300,
            scale: 43,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 7,
            group: module.exports.groups[9],
            name: "healing pad",
            desc: "standing on it will slowly heal you",
            req: ["wood", 30, "food", 10],
            ignoreCollision: true,
            healCol: 15,
            health: 400,
            colDiv: 0.7,
            scale: 45,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 9,
            group: module.exports.groups[10],
            name: "spawn pad",
            desc: "you will spawn here when you die but it will dissapear",
            req: ["wood", 100, "stone", 100],
            health: 400,
            ignoreCollision: true,
            spawnPoint: true,
            scale: 45,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 7,
            group: module.exports.groups[12],
            name: "blocker",
            desc: "blocks building in radius",
            req: ["wood", 30, "stone", 25],
            ignoreCollision: true,
            blocker: 300,
            health: 400,
            colDiv: 0.7,
            scale: 45,
            holdOffset: 20,
            placeOffset: -5
        }, {
            age: 7,
            group: module.exports.groups[13],
            name: "teleporter",
            desc: "teleports you to a random point on the map",
            req: ["wood", 60, "stone", 60],
            ignoreCollision: true,
            teleport: true,
            health: 200,
            colDiv: 0.7,
            scale: 45,
            holdOffset: 20,
            placeOffset: -5
        }];

        // ASSIGN IDS:
        for (var i = 0; i < module.exports.list.length; ++i) {
            module.exports.list[i].id = i;
            if (module.exports.list[i].pre) module.exports.list[i].pre = i - module.exports.list[i].pre;
        }
    },
});