// ==UserScript==
// @name         Moomoo.io Ultimate Extension
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  Ultimate moomoo.io extension combining the best features from multiple mods
// <AUTHOR> Mod Team
// @match        *://moomoo.io/*
// @match        *://*.moomoo.io/*
// @grant        none
// @require      https://rawgit.com/kawanet/msgpack-lite/master/dist/msgpack.min.js
// @icon         http://moomoo.io/img/icons/crown.png
// ==/UserScript==

(function() {
    'use strict';
    
    // Auto-verification system
    let verifyButton = document.querySelector("#altcha_checkbox");
    function autoVerify() {
        if (verifyButton) {
            verifyButton.click();
        }
    }
    setInterval(autoVerify, 1000);
    
    // Global variables
    let ws = null;
    let gameConfig = null;
    let players = [];
    let gameObjects = [];
    let bots = [];
    let settings = {
        // Combat settings
        autoAttack: false,
        autoHeal: true,
        autoBreak: false,
        autoPlace: false,
        autoReplace: false,
        
        // Visual settings
        showHealthBars: true,
        showTracers: false,
        showHitboxes: false,
        showBuildingHP: true,
        showBuildingOwner: true,
        
        // Bot settings
        enableBots: false,
        botCount: 0,
        botAutoAttack: false,
        botAutoHeal: true,
        
        // Misc settings
        autoAccept: false,
        autoEquip: false,
        menuTransparency: false
    };
    
    // WebSocket interception
    const originalWebSocket = window.WebSocket;
    window.WebSocket = function(url, protocols) {
        const socket = new originalWebSocket(url, protocols);
        
        // Store reference to main WebSocket
        if (url.includes('moomoo.io')) {
            ws = socket;
            console.log('WebSocket intercepted:', url);
            
            // Intercept messages
            const originalSend = socket.send;
            socket.send = function(data) {
                // Process outgoing packets here
                return originalSend.call(this, data);
            };
            
            socket.addEventListener('message', function(event) {
                try {
                    const data = window.msgpack.decode(new Uint8Array(event.data));
                    handleGamePacket(data);
                } catch (e) {
                    // Handle non-msgpack messages
                }
            });
        }
        
        return socket;
    };
    
    // Packet handling
    function handleGamePacket(data) {
        if (!data || !Array.isArray(data)) return;
        
        const packetType = data[0];
        
        switch (packetType) {
            case 'C': // Player spawn
                handlePlayerSpawn(data);
                break;
            case 'a': // Player update
                handlePlayerUpdate(data);
                break;
            case 'G': // Game objects
                handleGameObjects(data);
                break;
            case 'h': // Health update
                handleHealthUpdate(data);
                break;
        }
    }
    
    function handlePlayerSpawn(data) {
        // Handle player spawn logic
        console.log('Player spawned:', data);
    }
    
    function handlePlayerUpdate(data) {
        // Handle player position updates
        if (settings.autoHeal && data[6] < 80) { // Health below 80
            autoHeal();
        }
    }
    
    function handleGameObjects(data) {
        // Handle game object updates
        gameObjects = data.slice(1);
    }
    
    function handleHealthUpdate(data) {
        // Handle health updates
        if (settings.autoHeal && data[1] < 80) {
            autoHeal();
        }
    }
    
    // Auto functions
    function autoHeal() {
        if (ws && ws.readyState === WebSocket.OPEN) {
            // Send heal packet (food)
            ws.send(window.msgpack.encode(['K', 0]));
        }
    }
    
    function autoAttack() {
        if (ws && ws.readyState === WebSocket.OPEN) {
            // Send attack packet
            ws.send(window.msgpack.encode(['F', 1, 0, 1]));
        }
    }
    
    function autoPlace(itemId, angle) {
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(window.msgpack.encode(['G', itemId, true]));
            ws.send(window.msgpack.encode(['d', 1, angle]));
            ws.send(window.msgpack.encode(['G', 0, true]));
        }
    }
    
    // UI Creation
    function createUI() {
        const menuContainer = document.createElement('div');
        menuContainer.id = 'ultimateMenu';
        menuContainer.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 350px;
            max-height: 80vh;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #555;
            border-radius: 8px;
            color: white;
            font-family: Arial, sans-serif;
            z-index: 10000;
            overflow-y: auto;
            display: none;
        `;
        
        const header = document.createElement('div');
        header.style.cssText = `
            background: #333;
            padding: 10px;
            border-bottom: 1px solid #555;
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;
        header.innerHTML = `
            <h3 style="margin: 0; color: #4CAF50;">Ultimate Extension</h3>
            <button id="closeMenu" style="background: #f44336; border: none; color: white; padding: 5px 10px; border-radius: 3px; cursor: pointer;">×</button>
        `;
        
        const tabContainer = document.createElement('div');
        tabContainer.style.cssText = `
            display: flex;
            background: #222;
            border-bottom: 1px solid #555;
        `;
        
        const tabs = ['Combat', 'Visuals', 'Bots', 'Settings'];
        tabs.forEach((tab, index) => {
            const tabButton = document.createElement('button');
            tabButton.textContent = tab;
            tabButton.className = 'tab-button';
            tabButton.style.cssText = `
                flex: 1;
                padding: 10px;
                background: ${index === 0 ? '#4CAF50' : '#333'};
                border: none;
                color: white;
                cursor: pointer;
                border-right: 1px solid #555;
            `;
            tabButton.onclick = () => showTab(tab.toLowerCase());
            tabContainer.appendChild(tabButton);
        });
        
        const contentContainer = document.createElement('div');
        contentContainer.id = 'menuContent';
        contentContainer.style.cssText = `
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        `;
        
        menuContainer.appendChild(header);
        menuContainer.appendChild(tabContainer);
        menuContainer.appendChild(contentContainer);
        document.body.appendChild(menuContainer);
        
        // Close button functionality
        document.getElementById('closeMenu').onclick = () => {
            menuContainer.style.display = 'none';
        };
        
        // Show initial tab
        showTab('combat');
        
        return menuContainer;
    }
    
    function createCheckbox(label, settingKey, onChange) {
        const container = document.createElement('div');
        container.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        `;
        
        const labelElement = document.createElement('label');
        labelElement.textContent = label;
        labelElement.style.color = 'white';
        
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = settings[settingKey];
        checkbox.onchange = (e) => {
            settings[settingKey] = e.target.checked;
            if (onChange) onChange(e.target.checked);
            saveSettings();
        };
        
        container.appendChild(labelElement);
        container.appendChild(checkbox);
        return container;
    }
    
    function showTab(tabName) {
        const content = document.getElementById('menuContent');
        content.innerHTML = '';
        
        // Update tab button styles
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.style.background = btn.textContent.toLowerCase() === tabName ? '#4CAF50' : '#333';
        });
        
        switch (tabName) {
            case 'combat':
                showCombatTab(content);
                break;
            case 'visuals':
                showVisualsTab(content);
                break;
            case 'bots':
                showBotsTab(content);
                break;
            case 'settings':
                showSettingsTab(content);
                break;
        }
    }
    
    function showCombatTab(container) {
        container.appendChild(createCheckbox('Auto Attack', 'autoAttack'));
        container.appendChild(createCheckbox('Auto Heal', 'autoHeal'));
        container.appendChild(createCheckbox('Auto Break', 'autoBreak'));
        container.appendChild(createCheckbox('Auto Place', 'autoPlace'));
        container.appendChild(createCheckbox('Auto Replace', 'autoReplace'));
    }
    
    function showVisualsTab(container) {
        container.appendChild(createCheckbox('Show Health Bars', 'showHealthBars'));
        container.appendChild(createCheckbox('Show Tracers', 'showTracers'));
        container.appendChild(createCheckbox('Show Hitboxes', 'showHitboxes'));
        container.appendChild(createCheckbox('Show Building HP', 'showBuildingHP'));
        container.appendChild(createCheckbox('Show Building Owner', 'showBuildingOwner'));
    }
    
    function showBotsTab(container) {
        container.appendChild(createCheckbox('Enable Bots', 'enableBots'));
        container.appendChild(createCheckbox('Bot Auto Attack', 'botAutoAttack'));
        container.appendChild(createCheckbox('Bot Auto Heal', 'botAutoHeal'));

        const botCountContainer = document.createElement('div');
        botCountContainer.style.cssText = `
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        `;
        botCountContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <label style="color: white;">Bot Count: ${bots.length}</label>
                <div>
                    <button id="spawnBot" style="padding: 5px 10px; background: #4CAF50; border: none; color: white; border-radius: 3px; cursor: pointer; margin-right: 5px;">Spawn Bot</button>
                    <button id="disconnectBots" style="padding: 5px 10px; background: #f44336; border: none; color: white; border-radius: 3px; cursor: pointer;">Disconnect All</button>
                </div>
            </div>
            <div style="margin-top: 10px;">
                <label style="color: white; display: block; margin-bottom: 5px;">Spawn Count:</label>
                <input type="number" id="spawnCount" value="1" min="1" max="10" style="width: 60px; padding: 5px; border: none; border-radius: 3px;">
                <button id="spawnMultiple" style="margin-left: 10px; padding: 5px 10px; background: #2196F3; border: none; color: white; border-radius: 3px; cursor: pointer;">Spawn Multiple</button>
            </div>
        `;
        container.appendChild(botCountContainer);

        // Bot list
        const botListContainer = document.createElement('div');
        botListContainer.style.cssText = `
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            padding: 10px;
        `;

        if (bots.length > 0) {
            botListContainer.innerHTML = '<h4 style="color: white; margin: 0 0 10px 0;">Active Bots:</h4>';
            bots.forEach((bot, index) => {
                const botItem = document.createElement('div');
                botItem.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 5px;
                    margin: 5px 0;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 3px;
                `;
                botItem.innerHTML = `
                    <span style="color: white;">Bot ${bot.id} - ${bot.alive ? 'Alive' : 'Dead'} - HP: ${bot.health}/${bot.maxHealth}</span>
                    <button onclick="disconnectBot(${index})" style="padding: 3px 8px; background: #f44336; border: none; color: white; border-radius: 3px; cursor: pointer; font-size: 12px;">Disconnect</button>
                `;
                botListContainer.appendChild(botItem);
            });
        } else {
            botListContainer.innerHTML = '<p style="color: #888; text-align: center; margin: 0;">No bots active</p>';
        }

        container.appendChild(botListContainer);

        // Event listeners
        document.getElementById('spawnBot').onclick = spawnBot;
        document.getElementById('disconnectBots').onclick = disconnectAllBots;
        document.getElementById('spawnMultiple').onclick = () => {
            const count = parseInt(document.getElementById('spawnCount').value) || 1;
            for (let i = 0; i < Math.min(count, 10); i++) {
                setTimeout(() => spawnBot(), i * 500);
            }
        };
    }

    // Global function for bot disconnection
    window.disconnectBot = function(index) {
        if (bots[index]) {
            bots[index].disconnect();
            bots.splice(index, 1);
            showTab('bots'); // Refresh the tab
        }
    };
    
    function showSettingsTab(container) {
        container.appendChild(createCheckbox('Auto Accept', 'autoAccept'));
        container.appendChild(createCheckbox('Auto Equip', 'autoEquip'));
        container.appendChild(createCheckbox('Menu Transparency', 'menuTransparency', (enabled) => {
            const menu = document.getElementById('ultimateMenu');
            if (enabled) {
                menu.style.background = 'rgba(0, 0, 0, 0.7)';
            } else {
                menu.style.background = 'rgba(0, 0, 0, 0.9)';
            }
        }));

        // Keybind settings
        const keybindSection = document.createElement('div');
        keybindSection.style.cssText = `
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        `;
        keybindSection.innerHTML = `
            <h4 style="color: white; margin: 0 0 10px 0;">Keybinds</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; color: white; font-size: 14px;">
                <div>F1 - Toggle Menu</div>
                <div>E - Auto Attack</div>
                <div>Q - Auto Heal</div>
                <div>R - Auto Place</div>
                <div>T - Spawn Bot</div>
                <div>Y - Disconnect Bots</div>
            </div>
        `;
        container.appendChild(keybindSection);

        // Statistics section
        const statsSection = document.createElement('div');
        statsSection.style.cssText = `
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        `;
        statsSection.innerHTML = `
            <h4 style="color: white; margin: 0 0 10px 0;">Statistics</h4>
            <div style="color: white; font-size: 14px;">
                <div>Extension Version: 1.0.0</div>
                <div>Active Bots: ${bots.length}</div>
                <div>WebSocket Status: ${ws ? (ws.readyState === WebSocket.OPEN ? 'Connected' : 'Disconnected') : 'Not Available'}</div>
                <div>Settings Saved: ${localStorage.getItem('ultimateExtensionSettings') ? 'Yes' : 'No'}</div>
            </div>
        `;
        container.appendChild(statsSection);

        // Action buttons
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        `;

        const saveButton = document.createElement('button');
        saveButton.textContent = 'Save Settings';
        saveButton.style.cssText = `
            padding: 10px;
            background: #4CAF50;
            border: none;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        `;
        saveButton.onclick = () => {
            saveSettings();
            alert('Settings saved successfully!');
        };

        const resetButton = document.createElement('button');
        resetButton.textContent = 'Reset Settings';
        resetButton.style.cssText = `
            padding: 10px;
            background: #f44336;
            border: none;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        `;
        resetButton.onclick = () => {
            if (confirm('Are you sure you want to reset all settings?')) {
                localStorage.removeItem('ultimateExtensionSettings');
                location.reload();
            }
        };

        buttonContainer.appendChild(saveButton);
        buttonContainer.appendChild(resetButton);
        container.appendChild(buttonContainer);

        // Export/Import settings
        const importExportSection = document.createElement('div');
        importExportSection.style.cssText = `
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        `;
        importExportSection.innerHTML = `
            <h4 style="color: white; margin: 0 0 10px 0;">Import/Export Settings</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <button id="exportSettings" style="padding: 8px; background: #2196F3; border: none; color: white; border-radius: 4px; cursor: pointer;">Export</button>
                <button id="importSettings" style="padding: 8px; background: #FF9800; border: none; color: white; border-radius: 4px; cursor: pointer;">Import</button>
            </div>
            <textarea id="settingsData" style="width: 100%; height: 60px; margin-top: 10px; padding: 5px; border: none; border-radius: 4px; resize: vertical;" placeholder="Settings data will appear here..."></textarea>
        `;
        container.appendChild(importExportSection);

        // Export/Import event listeners
        document.getElementById('exportSettings').onclick = () => {
            const settingsData = JSON.stringify(settings, null, 2);
            document.getElementById('settingsData').value = settingsData;
        };

        document.getElementById('importSettings').onclick = () => {
            try {
                const settingsData = document.getElementById('settingsData').value;
                const importedSettings = JSON.parse(settingsData);
                settings = { ...settings, ...importedSettings };
                saveSettings();
                alert('Settings imported successfully!');
                showTab('settings'); // Refresh the tab
            } catch (error) {
                alert('Invalid settings data!');
            }
        };
    }
    
    // Bot management
    function spawnBot() {
        // Bot spawning logic would go here
        console.log('Spawning bot...');
        bots.push({ id: Date.now(), active: true });
        showTab('bots'); // Refresh the bots tab
    }
    
    // Settings management
    function saveSettings() {
        localStorage.setItem('ultimateExtensionSettings', JSON.stringify(settings));
        console.log('Settings saved');
    }
    
    function loadSettings() {
        const saved = localStorage.getItem('ultimateExtensionSettings');
        if (saved) {
            settings = { ...settings, ...JSON.parse(saved) };
        }
    }
    
    // Enhanced keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ignore if typing in input fields
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        switch (e.key) {
            case 'F1':
                e.preventDefault();
                const menu = document.getElementById('ultimateMenu');
                menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
                break;

            case 'e':
            case 'E':
                if (settings.autoAttack) {
                    autoAttack();
                }
                break;

            case 'q':
            case 'Q':
                if (settings.autoHeal) {
                    autoHeal();
                }
                break;

            case 'r':
            case 'R':
                if (settings.autoPlace) {
                    // Auto place spike (item 3) in front of player
                    autoPlace(3, 0);
                }
                break;

            case 't':
            case 'T':
                if (settings.enableBots) {
                    spawnBot();
                }
                break;

            case 'y':
            case 'Y':
                if (e.shiftKey) {
                    disconnectAllBots();
                }
                break;

            case 'Escape':
                // Close menu if open
                const menuElement = document.getElementById('ultimateMenu');
                if (menuElement.style.display !== 'none') {
                    menuElement.style.display = 'none';
                }
                break;
        }
    });

    // Mouse wheel zoom (hold Q + scroll)
    let qPressed = false;
    document.addEventListener('keydown', function(e) {
        if (e.key === 'q' || e.key === 'Q') {
            qPressed = true;
        }
    });

    document.addEventListener('keyup', function(e) {
        if (e.key === 'q' || e.key === 'Q') {
            qPressed = false;
        }
    });

    document.addEventListener('wheel', function(e) {
        if (qPressed) {
            e.preventDefault();
            // Zoom functionality would go here
            const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
            console.log('Zoom:', zoomFactor);
        }
    });

    // Performance monitoring
    function addPerformanceMonitoring() {
        let frameCount = 0;
        let lastTime = performance.now();
        let fps = 0;

        function updateFPS() {
            frameCount++;
            const currentTime = performance.now();

            if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;

                // Update FPS display if settings tab is open
                const fpsElement = document.getElementById('fpsDisplay');
                if (fpsElement) {
                    fpsElement.textContent = `FPS: ${fps}`;
                }
            }

            requestAnimationFrame(updateFPS);
        }

        updateFPS();
    }

    // Auto-save settings periodically
    function startAutoSave() {
        setInterval(() => {
            saveSettings();
        }, 30000); // Save every 30 seconds
    }

    // Notification system
    function showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 10001;
            opacity: 0;
            transition: opacity 0.3s ease;
            background: ${type === 'error' ? '#f44336' : type === 'success' ? '#4CAF50' : '#2196F3'};
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    }

    // Error handling
    window.addEventListener('error', function(e) {
        console.error('Extension error:', e.error);
        showNotification('Extension error occurred. Check console for details.', 'error');
    });

    // Unload cleanup
    window.addEventListener('beforeunload', function() {
        disconnectAllBots();
        saveSettings();
    });
    
    // Visual enhancements
    function addVisualEnhancements() {
        const canvas = document.getElementById('gameCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const originalRender = ctx.fillRect;

        // Override rendering to add visual enhancements
        ctx.fillRect = function(...args) {
            // Call original function
            originalRender.apply(this, args);

            // Add custom visuals
            if (settings.showHealthBars) {
                drawHealthBars(this);
            }
            if (settings.showTracers) {
                drawTracers(this);
            }
            if (settings.showHitboxes) {
                drawHitboxes(this);
            }
        };
    }

    function drawHealthBars(ctx) {
        // Health bar drawing logic
        players.forEach(player => {
            if (player.health && player.maxHealth) {
                const healthPercent = player.health / player.maxHealth;
                const barWidth = 60;
                const barHeight = 8;
                const x = player.x - barWidth / 2;
                const y = player.y - player.scale - 20;

                // Background
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.fillRect(x, y, barWidth, barHeight);

                // Health bar
                ctx.fillStyle = healthPercent > 0.5 ? '#4CAF50' : healthPercent > 0.25 ? '#FFC107' : '#F44336';
                ctx.fillRect(x, y, barWidth * healthPercent, barHeight);
            }
        });
    }

    function drawTracers(ctx) {
        // Tracer drawing logic
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        players.forEach(player => {
            if (player.isEnemy) {
                ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(player.x, player.y);
                ctx.stroke();
            }
        });
    }

    function drawHitboxes(ctx) {
        // Hitbox drawing logic
        gameObjects.forEach(obj => {
            if (obj.scale) {
                ctx.strokeStyle = 'rgba(255, 255, 0, 0.3)';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(obj.x, obj.y, obj.scale, 0, 2 * Math.PI);
                ctx.stroke();
            }
        });
    }

    // Advanced bot system
    class Bot {
        constructor(id) {
            this.id = id;
            this.ws = null;
            this.x = 0;
            this.y = 0;
            this.health = 100;
            this.maxHealth = 100;
            this.alive = false;
            this.target = null;
            this.lastAction = Date.now();
            this.weapons = [0];
            this.items = [0, 3, 6, 10];
            this.autoActions = {
                heal: true,
                attack: false,
                gather: false,
                build: false
            };
        }

        connect(serverUrl) {
            try {
                this.ws = new WebSocket(serverUrl);
                this.ws.binaryType = 'arraybuffer';

                this.ws.onopen = () => {
                    console.log(`Bot ${this.id} connected`);
                    this.spawn();
                };

                this.ws.onmessage = (event) => {
                    this.handleMessage(event);
                };

                this.ws.onclose = () => {
                    console.log(`Bot ${this.id} disconnected`);
                    this.alive = false;
                };

                this.ws.onerror = (error) => {
                    console.error(`Bot ${this.id} error:`, error);
                };

            } catch (error) {
                console.error(`Failed to connect bot ${this.id}:`, error);
            }
        }

        spawn() {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                // Send spawn packet
                this.send('M', {
                    name: `Bot_${this.id}`,
                    moofoll: 1,
                    skin: 0
                });
            }
        }

        send(type, data) {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(window.msgpack.encode([type, data]));
            }
        }

        handleMessage(event) {
            try {
                const data = window.msgpack.decode(new Uint8Array(event.data));
                const packetType = data[0];

                switch (packetType) {
                    case 'C': // Spawn confirmation
                        this.alive = true;
                        this.x = data[2];
                        this.y = data[3];
                        break;
                    case 'a': // Position update
                        this.updatePosition(data);
                        break;
                    case 'h': // Health update
                        this.updateHealth(data);
                        break;
                }

                this.performAutoActions();

            } catch (error) {
                // Handle non-msgpack messages
            }
        }

        updatePosition(data) {
            if (data[1] === this.id) {
                this.x = data[2];
                this.y = data[3];
            }
        }

        updateHealth(data) {
            if (data[0] === this.id) {
                this.health = data[1];
            }
        }

        performAutoActions() {
            const now = Date.now();
            if (now - this.lastAction < 100) return; // Throttle actions

            if (this.autoActions.heal && this.health < 80) {
                this.heal();
            }

            if (this.autoActions.attack && this.target) {
                this.attack();
            }

            this.lastAction = now;
        }

        heal() {
            this.send('K', 0); // Use food
        }

        attack() {
            if (this.target) {
                const angle = Math.atan2(this.target.y - this.y, this.target.x - this.x);
                this.send('F', [1, angle, 1]);
            }
        }

        moveTo(x, y) {
            const angle = Math.atan2(y - this.y, x - this.x);
            this.send('a', [angle, 1]);
        }

        disconnect() {
            if (this.ws) {
                this.ws.close();
                this.ws = null;
            }
        }
    }

    // Enhanced bot management
    function spawnBot() {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            console.error('Main WebSocket not connected');
            return;
        }

        const botId = Date.now();
        const bot = new Bot(botId);

        // Use the same server URL as main connection
        const serverUrl = ws.url;
        bot.connect(serverUrl);

        bots.push(bot);
        console.log(`Spawned bot ${botId}`);

        // Refresh bots tab if it's currently shown
        if (document.getElementById('menuContent').innerHTML.includes('Bot Count')) {
            showTab('bots');
        }
    }

    function disconnectAllBots() {
        bots.forEach(bot => bot.disconnect());
        bots = [];
        console.log('All bots disconnected');
    }

    // Chat commands
    function handleChatCommand(message) {
        const args = message.toLowerCase().split(' ');
        const command = args[0];

        switch (command) {
            case '!spawn':
                const count = parseInt(args[1]) || 1;
                for (let i = 0; i < count; i++) {
                    setTimeout(() => spawnBot(), i * 1000);
                }
                break;

            case '!disconnect':
                disconnectAllBots();
                break;

            case '!heal':
                bots.forEach(bot => bot.autoActions.heal = true);
                break;

            case '!attack':
                bots.forEach(bot => bot.autoActions.attack = true);
                break;

            case '!stop':
                bots.forEach(bot => {
                    bot.autoActions.attack = false;
                    bot.autoActions.gather = false;
                });
                break;
        }
    }

    // Enhanced packet handling
    function enhancedPacketHandling() {
        // Intercept chat messages for commands
        const originalSend = ws.send;
        ws.send = function(data) {
            try {
                const decoded = window.msgpack.decode(data);
                if (decoded[0] === 'ch' && decoded[1].startsWith('!')) {
                    handleChatCommand(decoded[1]);
                }
            } catch (e) {
                // Not a msgpack message
            }

            return originalSend.call(this, data);
        };
    }

    // Initialize
    function init() {
        try {
            loadSettings();
            createUI();
            addVisualEnhancements();
            addPerformanceMonitoring();
            startAutoSave();

            // Wait for WebSocket to be available
            const checkWebSocket = setInterval(() => {
                if (ws) {
                    enhancedPacketHandling();
                    clearInterval(checkWebSocket);
                    showNotification('Ultimate Extension connected to game!', 'success');
                }
            }, 1000);

            // Show welcome notification
            showNotification('Ultimate Extension loaded! Press F1 to open menu.', 'info', 5000);

            console.log('🎮 Ultimate Extension v1.0.0 loaded successfully!');
            console.log('📋 Available features:');
            console.log('  • Auto-verification system');
            console.log('  • Advanced bot management');
            console.log('  • Visual enhancements');
            console.log('  • Auto combat features');
            console.log('  • Customizable settings');
            console.log('⌨️  Keyboard shortcuts:');
            console.log('  • F1 - Toggle menu');
            console.log('  • E - Auto attack');
            console.log('  • Q - Auto heal');
            console.log('  • R - Auto place');
            console.log('  • T - Spawn bot');
            console.log('  • Shift+Y - Disconnect all bots');
            console.log('💬 Chat commands: !spawn [count], !disconnect, !heal, !attack, !stop');

        } catch (error) {
            console.error('Failed to initialize Ultimate Extension:', error);
            showNotification('Failed to load extension. Check console for details.', 'error');
        }
    }

    // Wait for page to load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
